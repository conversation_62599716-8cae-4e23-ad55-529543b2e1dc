"""
Django signals for automatic note creation when negotiations are created or updated.
"""

import logging
from typing import Any, Dict

from case_management.services.note_service import NoteService
from case_management.v2.models import CaseNegotiation, CaseNegotiationUIM
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver

User = get_user_model()
logger = logging.getLogger(__name__)

# Thread-local storage to track changes
import threading

_thread_local = threading.local()


def _get_field_changes(old_instance, new_instance) -> Dict[str, Dict[str, Any]]:
    """
    Compare old and new instances to find field changes.

    Args:
        old_instance: The instance before changes
        new_instance: The instance after changes

    Returns:
        Dictionary of changes {field: {'old': old_value, 'new': new_value}}
    """
    changes = {}

    # Get all fields from the model
    fields = [field.name for field in new_instance._meta.fields]

    for field in fields:
        old_value = getattr(old_instance, field, None) if old_instance else None
        new_value = getattr(new_instance, field, None)

        # Skip if values are the same
        if old_value == new_value:
            continue

        changes[field] = {"old": old_value, "new": new_value}

    return changes


@receiver(pre_save, sender=CaseNegotiation)
def store_original_negotiation(sender, instance, **kwargs):
    """Store the original instance before save for change tracking."""
    if instance.pk:  # Only for updates, not new instances
        try:
            original = CaseNegotiation.objects.get(pk=instance.pk)
            _thread_local.original_negotiation = original
        except CaseNegotiation.DoesNotExist:
            _thread_local.original_negotiation = None
    else:
        _thread_local.original_negotiation = None


@receiver(pre_save, sender=CaseNegotiationUIM)
def store_original_negotiation_uim(sender, instance, **kwargs):
    """Store the original instance before save for change tracking."""
    if instance.pk:  # Only for updates, not new instances
        try:
            original = CaseNegotiationUIM.objects.get(pk=instance.pk)
            _thread_local.original_negotiation_uim = original
        except CaseNegotiationUIM.DoesNotExist:
            _thread_local.original_negotiation_uim = None
    else:
        _thread_local.original_negotiation_uim = None


@receiver(post_save, sender=CaseNegotiation)
def create_negotiation_note(sender, instance, created, **kwargs):
    """
    Create automatic notes when CaseNegotiation is created or updated.

    Args:
        sender: The model class (CaseNegotiation)
        instance: The actual instance being saved
        created: Boolean indicating if this is a new instance
        **kwargs: Additional keyword arguments
    """
    try:
        # Skip if this is a migration or bulk operation
        if getattr(_thread_local, "skip_negotiation_signals", False):
            return

        # Get the user who created/updated the negotiation
        user = instance.created_by
        if not user:
            logger.warning(f"No created_by user found for negotiation {instance.id}")
            return

        if created:
            # Create note for new negotiation
            logger.info(f"Creating note for new negotiation {instance.id}")
            NoteService.create_negotiation_note(negotiation=instance, created_by=user)
        else:
            # Create note for updated negotiation
            original = getattr(_thread_local, "original_negotiation", None)
            if original:
                changes = _get_field_changes(original, instance)
                if changes:
                    logger.info(f"Creating update note for negotiation {instance.id}")
                    NoteService.create_negotiation_update_note(negotiation=instance, updated_by=user, changes=changes)

        # Clean up thread local storage
        if hasattr(_thread_local, "original_negotiation"):
            delattr(_thread_local, "original_negotiation")

    except Exception as e:
        logger.error(f"Error creating note for negotiation {instance.id}: {str(e)}", exc_info=True)
        # Don't raise the exception to avoid breaking the negotiation save


@receiver(post_save, sender=CaseNegotiationUIM)
def create_negotiation_uim_note(sender, instance, created, **kwargs):
    """
    Create automatic notes when CaseNegotiationUIM is created or updated.

    Args:
        sender: The model class (CaseNegotiationUIM)
        instance: The actual instance being saved
        created: Boolean indicating if this is a new instance
        **kwargs: Additional keyword arguments
    """
    try:
        # Skip if this is a migration or bulk operation
        if getattr(_thread_local, "skip_negotiation_signals", False):
            return

        # Get the user who created/updated the negotiation
        user = instance.created_by
        if not user:
            logger.warning(f"No created_by user found for UIM negotiation {instance.id}")
            return

        if created:
            # Create note for new UIM negotiation
            logger.info(f"Creating note for new UIM negotiation {instance.id}")
            NoteService.create_negotiation_note(negotiation=instance, created_by=user)
        else:
            # Create note for updated UIM negotiation
            original = getattr(_thread_local, "original_negotiation_uim", None)
            if original:
                changes = _get_field_changes(original, instance)
                if changes:
                    logger.info(f"Creating update note for UIM negotiation {instance.id}")
                    NoteService.create_negotiation_update_note(negotiation=instance, updated_by=user, changes=changes)

        # Clean up thread local storage
        if hasattr(_thread_local, "original_negotiation_uim"):
            delattr(_thread_local, "original_negotiation_uim")

    except Exception as e:
        logger.error(f"Error creating note for UIM negotiation {instance.id}: {str(e)}", exc_info=True)
        # Don't raise the exception to avoid breaking the negotiation save


class DisableNegotiationSignals:
    """
    Context manager to temporarily disable negotiation signals.
    Useful for bulk operations or migrations.

    Usage:
        with DisableNegotiationSignals():
            # Bulk operations here won't trigger note creation
            CaseNegotiation.objects.bulk_create(negotiations)
    """

    def __enter__(self):
        _thread_local.skip_negotiation_signals = True
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        _thread_local.skip_negotiation_signals = False


def disable_negotiation_signals():
    """
    Decorator to disable negotiation signals for a function.

    Usage:
        @disable_negotiation_signals()
        def bulk_create_negotiations():
            # This function won't trigger note creation
            pass
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            with DisableNegotiationSignals():
                return func(*args, **kwargs)

        return wrapper

    return decorator


# Import post_delete signal
from django.db.models.signals import post_delete


@receiver(post_delete, sender=CaseNegotiation)
def create_negotiation_delete_note(sender, instance, **kwargs):
    """
    Create automatic notes when CaseNegotiation is deleted.

    Args:
        sender: The model class (CaseNegotiation)
        instance: The actual instance being deleted
        **kwargs: Additional keyword arguments
    """
    try:
        # Skip if this is a migration or bulk operation
        if getattr(_thread_local, "skip_negotiation_signals", False):
            return

        # Get the user who deleted the negotiation (if available)
        # Note: Django doesn't provide the deleting user in post_delete signal
        # We'll use the created_by user as fallback
        user = instance.created_by
        if not user:
            logger.warning(f"No created_by user found for deleted negotiation {instance.id}")
            return

        logger.info(f"Creating delete note for negotiation {instance.id}")
        NoteService.create_negotiation_delete_note(negotiation=instance, deleted_by=user)

    except Exception as e:
        logger.error(f"Error creating delete note for negotiation {instance.id}: {str(e)}", exc_info=True)
        # Don't raise the exception to avoid breaking the negotiation delete


@receiver(post_delete, sender=CaseNegotiationUIM)
def create_negotiation_uim_delete_note(sender, instance, **kwargs):
    """
    Create automatic notes when CaseNegotiationUIM is deleted.

    Args:
        sender: The model class (CaseNegotiationUIM)
        instance: The actual instance being deleted
        **kwargs: Additional keyword arguments
    """
    try:
        # Skip if this is a migration or bulk operation
        if getattr(_thread_local, "skip_negotiation_signals", False):
            return

        # Get the user who deleted the negotiation (if available)
        user = instance.created_by
        if not user:
            logger.warning(f"No created_by user found for deleted UIM negotiation {instance.id}")
            return

        logger.info(f"Creating delete note for UIM negotiation {instance.id}")
        NoteService.create_negotiation_delete_note(negotiation=instance, deleted_by=user)

    except Exception as e:
        logger.error(f"Error creating delete note for UIM negotiation {instance.id}: {str(e)}", exc_info=True)
        # Don't raise the exception to avoid breaking the negotiation delete
