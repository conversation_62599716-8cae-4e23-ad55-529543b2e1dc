#!/usr/bin/env python
"""
Test script to verify automatic note creation for negotiations.
Run this script to test the negotiation note creation functionality.
"""
import os
import sys
import django
from decimal import Decimal

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'alphalaw.settings')
django.setup()

from django.contrib.auth import get_user_model
from case_management.models import Case, CaseNote
from case_management.v2.models import (
    CaseNegotiation, 
    CaseNegotiationUIM, 
    CaseDefendant, 
    ClientInsurance,
    InsuranceCompany
)

User = get_user_model()


def test_negotiation_note_creation():
    """Test automatic note creation for negotiations."""
    print("🧪 Testing automatic note creation for negotiations...")
    
    try:
        # Get a test user
        user = User.objects.first()
        if not user:
            print("❌ No users found. Please create a user first.")
            return False
            
        print(f"✅ Using test user: {user.email}")
        
        # Get a test case
        case = Case.objects.first()
        if not case:
            print("❌ No cases found. Please create a case first.")
            return False
            
        print(f"✅ Using test case: {case.id}")
        
        # Test 1: Create a regular negotiation (third-party)
        print("\n📝 Test 1: Creating regular negotiation...")
        
        defendant = CaseDefendant.objects.filter(case=case).first()
        if defendant:
            insurance_company = InsuranceCompany.objects.first()
            
            # Count notes before
            notes_before = CaseNote.objects.filter(case=case, note_for="Negotiation").count()
            
            # Create negotiation
            negotiation = CaseNegotiation.objects.create(
                defendant=defendant,
                type="INITIAL_DEMAND",
                amount=Decimal("75000.00"),
                status="SENT",
                created_by=user,
                insurance_company=insurance_company,
                notes="Test demand for automatic note creation"
            )
            
            # Count notes after
            notes_after = CaseNote.objects.filter(case=case, note_for="Negotiation").count()
            
            if notes_after > notes_before:
                print(f"✅ Regular negotiation note created! Notes: {notes_before} → {notes_after}")
                
                # Get the latest note
                latest_note = CaseNote.objects.filter(
                    case=case, 
                    note_for="Negotiation"
                ).order_by('-created_at').first()
                
                print(f"   📄 Note title: {latest_note.title}")
                print(f"   📄 Note content preview: {latest_note.content[:100]}...")
            else:
                print(f"❌ Regular negotiation note NOT created. Notes: {notes_before} → {notes_after}")
        else:
            print("⚠️  No defendants found for regular negotiation test")
        
        # Test 2: Create a UIM negotiation
        print("\n📝 Test 2: Creating UIM negotiation...")
        
        client_insurance = ClientInsurance.objects.filter(case=case).first()
        if client_insurance:
            # Count notes before
            notes_before = CaseNote.objects.filter(case=case, note_for="Negotiations_UIM").count()
            
            # Create UIM negotiation
            uim_negotiation = CaseNegotiationUIM.objects.create(
                client_insurance=client_insurance,
                type="INITIAL_DEMAND",
                amount=Decimal("50000.00"),
                status="SENT",
                created_by=user,
                insurance_company=client_insurance.insurance_company,
                notes="Test UIM demand for automatic note creation"
            )
            
            # Count notes after
            notes_after = CaseNote.objects.filter(case=case, note_for="Negotiations_UIM").count()
            
            if notes_after > notes_before:
                print(f"✅ UIM negotiation note created! Notes: {notes_before} → {notes_after}")
                
                # Get the latest note
                latest_note = CaseNote.objects.filter(
                    case=case, 
                    note_for="Negotiations_UIM"
                ).order_by('-created_at').first()
                
                print(f"   📄 Note title: {latest_note.title}")
                print(f"   📄 Note content preview: {latest_note.content[:100]}...")
            else:
                print(f"❌ UIM negotiation note NOT created. Notes: {notes_before} → {notes_after}")
        else:
            print("⚠️  No client insurance found for UIM negotiation test")
        
        # Test 3: Update a negotiation
        print("\n📝 Test 3: Updating negotiation...")
        
        if defendant:
            # Count notes before
            notes_before = CaseNote.objects.filter(case=case, note_for="Negotiation").count()
            
            # Update the negotiation
            negotiation.amount = Decimal("80000.00")
            negotiation.status = "RECEIVED"
            negotiation.save()
            
            # Count notes after
            notes_after = CaseNote.objects.filter(case=case, note_for="Negotiation").count()
            
            if notes_after > notes_before:
                print(f"✅ Negotiation update note created! Notes: {notes_before} → {notes_after}")
                
                # Get the latest note
                latest_note = CaseNote.objects.filter(
                    case=case, 
                    note_for="Negotiation"
                ).order_by('-created_at').first()
                
                print(f"   📄 Note title: {latest_note.title}")
                print(f"   📄 Note content preview: {latest_note.content[:100]}...")
            else:
                print(f"❌ Negotiation update note NOT created. Notes: {notes_before} → {notes_after}")
        
        print("\n🎉 Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_negotiation_note_creation()
    sys.exit(0 if success else 1)
