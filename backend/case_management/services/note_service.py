"""
Service for creating and managing case notes automatically.
"""

import logging
from decimal import Decimal
from typing import Any, Dict, Optional, Union

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone

from case_management.models import CaseNote
from case_management.v2.models import CaseDefendant, CaseNegotiation, CaseNegotiationUIM

User = get_user_model()
logger = logging.getLogger(__name__)


class NoteService:
    """Service for creating and managing case notes."""

    @staticmethod
    @transaction.atomic
    def create_negotiation_note(
        negotiation: Union[CaseNegotiation, CaseNegotiationUIM],
        created_by: User,
        title: Optional[str] = None,
        content: Optional[str] = None,
        note_for: Optional[str] = None,
        defendant: Optional[CaseDefendant] = None,
    ) -> CaseNote:
        """
        Create a note for a negotiation (demand or offer).

        Args:
            negotiation: The negotiation object (CaseNegotiation or CaseNegotiationUIM)
            created_by: The user creating the note
            title: Optional custom title for the note
            content: Optional custom content for the note
            note_for: The note category (Negotiation or Negotiations_UIM)
            defendant: Optional defendant for regular negotiations

        Returns:
            The created CaseNote object
        """
        # Determine if this is a UIM negotiation
        is_uim = isinstance(negotiation, CaseNegotiationUIM)

        # Get the case and set note category
        if is_uim:
            case = negotiation.client_insurance.case
            note_for = "Negotiations_UIM"
            defendant = None
        else:
            case = negotiation.defendant.case
            note_for = "Negotiation"
            defendant = negotiation.defendant

        # Generate automatic title if not provided
        if not title:
            title = NoteService._generate_negotiation_title(negotiation, is_uim)

        # Generate automatic content if not provided
        if not content:
            content = NoteService._generate_negotiation_content(negotiation, is_uim)

        # Create the note
        note = CaseNote.objects.create(
            case=case,
            title=title,
            content=content,
            created_by=created_by,
            note_for=note_for,
            defendant=defendant,
            is_editable=True,
        )

        logger.info(f"Created automatic note {note.id} for negotiation {negotiation.id}")
        return note

    @staticmethod
    def create_negotiation_update_note(
        negotiation: Union[CaseNegotiation, CaseNegotiationUIM],
        updated_by: User,
        changes: Dict[str, Dict[str, Any]],
        title: Optional[str] = None,
        content: Optional[str] = None,
    ) -> Optional[CaseNote]:
        """
        Create a note for a negotiation update.

        Args:
            negotiation: The updated negotiation object
            updated_by: The user who updated the negotiation
            changes: Dictionary of field changes {field: {'old': old_value, 'new': new_value}}
            title: Optional custom title for the note
            content: Optional custom content for the note

        Returns:
            The created CaseNote object or None if no significant changes
        """
        # Filter out insignificant changes
        significant_changes = NoteService._filter_significant_changes(changes)

        if not significant_changes:
            logger.debug(f"No significant changes found for negotiation {negotiation.id}")
            return None

        # Determine if this is a UIM negotiation
        is_uim = isinstance(negotiation, CaseNegotiationUIM)

        # Get the case and set note category
        if is_uim:
            case = negotiation.client_insurance.case
            note_for = "Negotiations_UIM"
            defendant = None
        else:
            case = negotiation.defendant.case
            note_for = "Negotiation"
            defendant = negotiation.defendant

        # Generate automatic title if not provided
        if not title:
            title = NoteService._generate_update_title(negotiation, is_uim, significant_changes)

        # Generate automatic content if not provided
        if not content:
            content = NoteService._generate_update_content(negotiation, is_uim, significant_changes, updated_by)

        # Create the note
        note = CaseNote.objects.create(
            case=case,
            title=title,
            content=content,
            created_by=updated_by,
            note_for=note_for,
            defendant=defendant,
            is_editable=True,
        )

        logger.info(f"Created update note {note.id} for negotiation {negotiation.id}")
        return note

    @staticmethod
    def _generate_negotiation_title(negotiation: Union[CaseNegotiation, CaseNegotiationUIM], is_uim: bool) -> str:
        """Generate an automatic title for a negotiation note."""
        negotiation_type = negotiation.get_type_display()
        amount = f"${negotiation.amount:,.2f}"

        if is_uim:
            insurance_name = negotiation.insurance_company.name if negotiation.insurance_company else "UIM Insurance"
            return f"{negotiation_type} of {amount} - {insurance_name}"
        else:
            if negotiation.defendant:
                if negotiation.defendant.company_entity:
                    defendant_name = negotiation.defendant.company_entity
                else:
                    defendant_name = f"{negotiation.defendant.first_name} {negotiation.defendant.last_name}"
            else:
                defendant_name = "Defendant"
            insurance_name = negotiation.insurance_company.name if negotiation.insurance_company else "Insurance"
            return f"{negotiation_type} of {amount}"

    @staticmethod
    def _generate_negotiation_content(negotiation: Union[CaseNegotiation, CaseNegotiationUIM], is_uim: bool) -> str:
        """Generate automatic content for a negotiation note."""
        content_parts = []

        # Basic negotiation info
        negotiation_type = negotiation.get_type_display()
        amount = f"${negotiation.amount:,.2f}"
        status = negotiation.get_status_display()

        if is_uim:
            insurance_name = negotiation.insurance_company.name if negotiation.insurance_company else "UIM Insurance"
            content_parts.append(
                f"<p><strong>{negotiation_type}</strong> of <strong>{amount}</strong> {status.lower()} for UIM claim with {insurance_name}</p>"
            )
        else:
            if negotiation.defendant:
                if negotiation.defendant.company_entity:
                    defendant_name = negotiation.defendant.company_entity
                else:
                    defendant_name = f"{negotiation.defendant.first_name} {negotiation.defendant.last_name}"
            else:
                defendant_name = "Defendant"
            insurance_name = (
                negotiation.insurance_company.name if negotiation.insurance_company else "Insurance Company"
            )
            content_parts.append(
                f"<p><strong>{negotiation_type}</strong> of <strong>{amount}</strong> {status.lower()} to {defendant_name} via {insurance_name}</p>"
            )

        # Add date information
        if negotiation.type == "INITIAL_DEMAND" and negotiation.date_sent:
            date_sent_str = (
                negotiation.date_sent.strftime("%B %d, %Y")
                if hasattr(negotiation.date_sent, "strftime")
                else str(negotiation.date_sent)
            )
            content_parts.append(f"<p><strong>Date Sent:</strong> {date_sent_str}</p>")
            if negotiation.response_deadline:
                deadline_str = (
                    negotiation.response_deadline.strftime("%B %d, %Y")
                    if hasattr(negotiation.response_deadline, "strftime")
                    else str(negotiation.response_deadline)
                )
                content_parts.append(f"<p><strong>Response Deadline:</strong> {deadline_str}</p>")
        elif negotiation.type in ["OFFER", "COUNTER_OFFER"] and negotiation.response_received_date:
            received_date_str = (
                negotiation.response_received_date.strftime("%B %d, %Y")
                if hasattr(negotiation.response_received_date, "strftime")
                else str(negotiation.response_received_date)
            )
            content_parts.append(f"<p><strong>Date Received:</strong> {received_date_str}</p>")

        # Add adjuster information
        if negotiation.adjuster:
            content_parts.append(f"<p><strong>Adjuster:</strong> {negotiation.adjuster}</p>")

        # Add settlement terms if available
        if negotiation.settlement_terms:
            content_parts.append(f"<p><strong>Settlement Terms:</strong> {negotiation.settlement_terms}</p>")

        # Add payment terms if available
        if negotiation.payment_terms:
            content_parts.append(f"<p><strong>Payment Terms:</strong> {negotiation.payment_terms}</p>")

        # Add conditions if available
        if negotiation.conditions:
            content_parts.append(f"<p><strong>Conditions:</strong> {negotiation.conditions}</p>")

        # Add release terms if available
        if negotiation.release_terms:
            content_parts.append(f"<p><strong>Release Terms:</strong> {negotiation.release_terms}</p>")

        # Add any custom notes from the negotiation
        if negotiation.notes:
            content_parts.append(f"<p><strong>Additional Notes:</strong> {negotiation.notes}</p>")

        # Add assignment information
        if negotiation.assigned_to:
            content_parts.append(f"<p><strong>Assigned To:</strong> {negotiation.assigned_to.get_full_name()}</p>")

        return "".join(content_parts)

    @staticmethod
    def _generate_update_title(
        negotiation: Union[CaseNegotiation, CaseNegotiationUIM], is_uim: bool, changes: Dict[str, Dict[str, Any]]
    ) -> str:
        """Generate an automatic title for a negotiation update note."""
        negotiation_type = negotiation.get_type_display()
        amount = f"${negotiation.amount:,.2f}"

        # Determine what was updated
        if "amount" in changes:
            return f"{negotiation_type} Amount Updated - {amount}"
        elif "status" in changes:
            return f"{negotiation_type} Status Updated - {amount}"
        else:
            return f"{negotiation_type} Updated - {amount}"

    @staticmethod
    def _generate_update_content(
        negotiation: Union[CaseNegotiation, CaseNegotiationUIM],
        is_uim: bool,
        changes: Dict[str, Dict[str, Any]],
        updated_by: User,
    ) -> str:
        """Generate automatic content for a negotiation update note."""
        content_parts = [
            "<p><strong>Negotiation Updated</strong></p>",
            f"<p><strong>Updated By:</strong> {updated_by.get_full_name()}</p>",
            f"<p><strong>Update Date:</strong> {timezone.now().strftime('%B %d, %Y at %I:%M %p')}</p>",
        ]

        # Add change details
        content_parts.append("<p><strong>Changes Made:</strong></p>")
        content_parts.append("<ul>")

        for field, change in changes.items():
            field_display = NoteService._get_field_display_name(field)
            old_value = NoteService._format_field_value(field, change["old"])
            new_value = NoteService._format_field_value(field, change["new"])
            content_parts.append(f"<li><strong>{field_display}:</strong> {old_value} → {new_value}</li>")

        content_parts.append("</ul>")
        return "".join(content_parts)

    @staticmethod
    def _filter_significant_changes(changes: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Filter out insignificant changes like updated_at, created_at, etc."""
        significant_fields = {
            "amount",
            "status",
            "type",
            "date_sent",
            "response_deadline",
            "response_received_date",
            "extension_date",
            "settlement_terms",
            "payment_terms",
            "conditions",
            "release_terms",
            "notes",
            "assigned_to",
            "adjuster",
            "insurance_company",
            "accepted_date",
            "accepted_by",
        }

        return {
            field: change
            for field, change in changes.items()
            if field in significant_fields and change["old"] != change["new"]
        }

    @staticmethod
    def _get_field_display_name(field: str) -> str:
        """Get human-readable field names."""
        field_names = {
            "amount": "Amount",
            "status": "Status",
            "type": "Type",
            "date_sent": "Date Sent",
            "response_deadline": "Response Deadline",
            "response_received_date": "Date Received",
            "extension_date": "Extension Date",
            "settlement_terms": "Settlement Terms",
            "payment_terms": "Payment Terms",
            "conditions": "Conditions",
            "release_terms": "Release Terms",
            "notes": "Notes",
            "assigned_to": "Assigned To",
            "adjuster": "Adjuster",
            "insurance_company": "Insurance Company",
            "accepted_date": "Accepted Date",
            "accepted_by": "Accepted By",
        }
        return field_names.get(field, field.replace("_", " ").title())

    @staticmethod
    def _format_field_value(field: str, value: Any) -> str:
        """Format field values for display in notes."""
        if value is None:
            return "None"

        if field == "amount" and isinstance(value, (int, float, Decimal)):
            return f"${value:,.2f}"

        if field in ["date_sent", "response_deadline", "response_received_date", "extension_date", "accepted_date"]:
            if hasattr(value, "strftime"):
                return value.strftime("%B %d, %Y")
            else:
                return str(value)

        if field in ["assigned_to", "accepted_by"] and hasattr(value, "get_full_name"):
            return value.get_full_name()

        if field in ["adjuster", "insurance_company"] and hasattr(value, "name"):
            return value.name

        return str(value)
