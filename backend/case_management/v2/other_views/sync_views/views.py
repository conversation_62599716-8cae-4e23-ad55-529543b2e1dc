import uuid
from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from utils.utils import info_logger

from case_management.cards.models import UserCard
from case_management.models import Case, CaseNote, Task
from case_management.v2.models import (
    CaseAttorneyLien,
    CaseDefendant,
    CaseDefendantLegalRepresentation,
    CaseExpertWitness,
    CaseIncidentDetails,
    CaseMiscellaneousLien,
    CaseParty,
    CasePartyContact,
    CaseWorkers,
    ClientInsurance,
    ClientPropertyDamage,
    DefendantInsurance,
    DefendantInsuranceAdjuster,
    DefendantPropertyDamage,
    HealthInsurance,
    InsuranceAdjuster,
    InsuranceLegalRepresentation,
    SyncedObject,
    TreatmentProvider,
)
from case_management.v2.serializers import (
    CaseAttorneyLienSerializer,
    CaseIncidentDetailsSerializer,
    CaseMiscellaneousLienSerializer,
    CasePartySerializer,
    NoteSyncSerializer,
    TaskSyncSerializer,
    TreatmentProviderSerializer,
    UserCardSyncSerializer,
)

__all__ = [
    "CaseDefendantSyncViewSet",
    "CaseNoteSyncViewSet",
    "TaskSyncViewSet",
    "UserCardSyncViewSet",
    "ClientSyncViewSet",
    "CaseIncidentDetailsSyncViewSet",
    "CasePartySyncViewSet",
    "TreatmentProviderSyncViewSet",
    "CaseLiensSyncViewSet",
    "CaseWorkersSyncViewSet",
    "CaseStatusSyncViewSet",
    "CaseExpertWitnessSyncViewSet",
    "CaseAttorneyLienSyncViewSet",
    "CaseMiscellaneousLienSyncViewSet",
    "CaseHealthInsuranceSyncViewSet",
]


class CaseSyncMixin:
    """Simplified sync mixin that only handles explicit sync operations"""

    def get_all_synced_objects(self, obj, content_type=None, visited_ids=None, sync_group_ids=None):
        """
        Get all synced objects related to the given object (both direct and transitive).

        Args:
            obj: The source object to find syncs for
            content_type: ContentType of the object (will be determined if not provided)
            visited_ids: Set of object IDs already visited (to prevent cycles)
            sync_group_ids: Set of sync_group IDs already processed

        Returns:
            dict: Dictionary mapping object IDs to their corresponding objects
        """

        if visited_ids is None:
            visited_ids = set()

        if sync_group_ids is None:
            sync_group_ids = set()

        if obj.id in visited_ids:
            return {}

        visited_ids.add(obj.id)

        if content_type is None:
            content_type = ContentType.objects.get_for_model(obj.__class__)

        # Get direct sync relationships
        direct_syncs = SyncedObject.objects.filter(content_type=content_type, source_object_id=obj.id)

        result = {}

        # Process each direct sync
        for sync in direct_syncs:
            result[sync.id] = sync

        return result

    def get_or_create_sync_relationship(self, source_obj, target_obj, content_type=None):
        """
        Get existing sync relationship or create a new one between source and target objects.
        Uses transitive relationships if available.

        Args:
            source_obj: Source object
            target_obj: Target object
            content_type: ContentType of the objects (will be determined if not provided)

        Returns:
            tuple: (sync_exists, sync_group)
            - sync_exists: Boolean indicating if relationship already existed
            - sync_group: UUID of the sync group
        """

        if content_type is None:
            content_type = ContentType.objects.get_for_model(source_obj.__class__)

        # First check for direct relationship
        existing_sync = SyncedObject.objects.filter(
            content_type=content_type, source_object_id=source_obj.id, target_object_id=target_obj.id
        ).first()

        if existing_sync:
            return True, existing_sync.sync_group

        # No existing relationship, create new sync group
        sync_group = uuid.uuid4()
        SyncedObject.objects.get_or_create(
            content_type=content_type,
            source_object_id=source_obj.id,
            target_object_id=target_obj.id,
            defaults={
                "sync_group": sync_group,
            },
        )

        SyncedObject.objects.get_or_create(
            content_type=content_type,
            source_object_id=target_obj.id,
            target_object_id=source_obj.id,
            defaults={
                "sync_group": sync_group,
            },
        )

        return False, sync_group


class CaseDefendantSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """
    ViewSet for syncing defendants between cases.
    Simplified to provide a single sync API endpoint.
    """

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync a defendant from one case to multiple target cases.
        """
        print("\n=== Starting Sync Process ===")

        # Validate input
        source_defendant_id = request.data.get("source_defendant_id")
        print(f"\nSource defendant ID: {source_defendant_id}, source case ID: {case_id}")

        case = Case.objects.get(id=case_id)
        target_case_ids = request.data.get("target_case_ids", [])
        print(f"\n\nTarget case IDs: {target_case_ids}\n\n")

        if not source_defendant_id:
            return Response({"error": "source_defendant_id is required"}, status=400)

        if not target_case_ids:
            return Response({"error": "target_case_ids is required"}, status=400)

        try:
            # Get source defendant
            source_defendant = CaseDefendant.objects.get(id=source_defendant_id, case_id=case_id)

            # Get target cases
            target_cases = Case.objects.filter(id__in=target_case_ids)

            if not target_cases.exists():
                return Response({"error": "No valid target cases found"}, status=400)

            synced_objects = []
            errors = []

            for target_case in target_cases:
                try:
                    print(f"\nSyncing to target case: {target_case.id}")

                    # Get ContentType for CaseDefendant
                    content_type = ContentType.objects.get_for_model(CaseDefendant)

                    # Check if this defendant already has synced objects in the target case
                    synced_objects_dict = self.get_all_synced_objects(source_defendant, content_type)

                    # Try to find a target defendant in the synced objects that belongs to the target case
                    target_defendant = None
                    for obj_id, obj in synced_objects_dict.items():
                        if obj.target_object_id in target_case.v2_defendants.values_list("id", flat=True):
                            target_defendant = CaseDefendant.objects.get(id=obj.target_object_id)
                            print(
                                f"Found                              existing synced defendant {target_defendant.id} in target case through transitive relationship"
                            )
                            break

                    # If no target defendant found through transitive relationships, check for similar defendants
                    if not target_defendant:
                        target_defendant = CaseDefendant.objects.create(
                            case=target_case,
                            first_name=source_defendant.first_name,
                            last_name=source_defendant.last_name,
                            defendant_type=source_defendant.defendant_type,
                            company_entity=source_defendant.company_entity,
                            description=source_defendant.description,
                            middle_name=source_defendant.middle_name,
                            registered_agent=source_defendant.registered_agent,
                            gender=source_defendant.gender,
                            ssn=source_defendant.ssn,
                            date_of_birth=source_defendant.date_of_birth,
                            driver_license_type=source_defendant.driver_license_type,
                            driver_license=source_defendant.driver_license,
                            language=source_defendant.language,
                            race=source_defendant.race,
                            street1=source_defendant.street1,
                            street2=source_defendant.street2,
                            city=source_defendant.city,
                            state=source_defendant.state,
                            zip_code=source_defendant.zip_code,
                            phone=source_defendant.phone,
                            phone_ext=source_defendant.phone_ext,
                            cell=source_defendant.cell,
                            fax=source_defendant.fax,
                            work_phone=source_defendant.work_phone,
                            email=source_defendant.email,
                            no_insurance=source_defendant.no_insurance,
                        )
                        print(f"Created new defendant {target_defendant.id} in target case")

                        # Create sync relationship using the method
                        _, sync_group = self.get_or_create_sync_relationship(
                            source_defendant, target_defendant, content_type
                        )
                        print(
                            f"Created/updated sync relationship between {source_defendant.id} and {target_defendant.id}"
                        )
                    else:
                        # Update the target defendant with source data
                        target_defendant.first_name = source_defendant.first_name
                        target_defendant.last_name = source_defendant.last_name
                        target_defendant.defendant_type = source_defendant.defendant_type
                        target_defendant.company_entity = source_defendant.company_entity
                        target_defendant.description = source_defendant.description
                        target_defendant.middle_name = source_defendant.middle_name
                        target_defendant.registered_agent = source_defendant.registered_agent
                        target_defendant.gender = source_defendant.gender
                        target_defendant.ssn = source_defendant.ssn
                        target_defendant.date_of_birth = source_defendant.date_of_birth
                        target_defendant.driver_license_type = source_defendant.driver_license_type
                        target_defendant.driver_license = source_defendant.driver_license
                        target_defendant.language = source_defendant.language
                        target_defendant.race = source_defendant.race
                        target_defendant.street1 = source_defendant.street1
                        target_defendant.street2 = source_defendant.street2
                        target_defendant.city = source_defendant.city
                        target_defendant.state = source_defendant.state
                        target_defendant.zip_code = source_defendant.zip_code
                        target_defendant.phone = source_defendant.phone
                        target_defendant.phone_ext = source_defendant.phone_ext
                        target_defendant.cell = source_defendant.cell
                        target_defendant.fax = source_defendant.fax
                        target_defendant.work_phone = source_defendant.work_phone
                        target_defendant.email = source_defendant.email
                        target_defendant.no_insurance = source_defendant.no_insurance
                        target_defendant.save()
                        print(f"Updated existing defendant {target_defendant.id}")

                    # Sync related objects if requested
                    self._sync_insurances(source_defendant, target_defendant, True)

                    self._sync_property_damage(source_defendant, target_defendant)

                    self._sync_legal_representation(source_defendant, target_defendant)

                    synced_objects.append(target_case.id)

                except Exception as e:
                    print(f"Error syncing to case {target_case.id}: {str(e)}")
                    # get complete error from the e
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}
                    errors.append({"case_id": target_case.id, "error": str(error_details)})
                    continue

            return Response(
                {
                    "message": "Sync completed",
                    "synced_cases": [f"{obj}-synced" for obj in synced_objects],
                    "errors": errors if errors else None,
                }
            )

        except CaseDefendant.DoesNotExist:
            return Response({"error": "Source defendant not found"}, status=404)
        except Exception as e:
            print(f"Error in sync process: {str(e)}")
            return Response({"error": f"Failed to sync defendant: {str(e)}"}, status=400)

    def _sync_insurances(self, source_defendant, target_defendant, sync_adjusters=True):
        """Sync insurances from source to target defendant"""
        print(f"\n=== Syncing insurances for defendant {source_defendant.id} ===")

        # Get source insurances
        source_insurances = DefendantInsurance.objects.filter(defendant=source_defendant)

        if not source_insurances.exists():
            print(f"No insurances found for source defendant {source_defendant.id}")
            return

        # Get ContentType for DefendantInsurance
        content_type = ContentType.objects.get_for_model(DefendantInsurance)
        synced_ids = set()  # Track which target insurances we've processed

        # Process each source insurance
        for source_insurance in source_insurances:
            # Check if this insurance already has synced objects linked to the target defendant
            synced_objects = self.get_all_synced_objects(source_insurance, content_type)

            # Try to find a target insurance in the synced objects that belongs to the target defendant
            target_insurance = None
            for obj_id, obj in synced_objects.items():
                if obj.target_object_id in target_defendant.insurances.values_list("id", flat=True):
                    target_insurance = DefendantInsurance.objects.get(id=obj.target_object_id)
                    print(f"Found existing synced insurance {target_insurance.id} through transitive relationship")
                    break

            # If no existing relationship found through transitive relationships
            if not target_insurance:
                # Create a new insurance
                print("Creating new insurance for target defendant")
                target_insurance = DefendantInsurance.objects.create(
                    defendant=target_defendant,
                    no_insurance=source_insurance.no_insurance,
                    umbrella_policy=source_insurance.umbrella_policy,
                    claim_number=source_insurance.claim_number,
                    policy_number=source_insurance.policy_number,
                    insured=source_insurance.insured,
                    policy_limits=source_insurance.policy_limits,
                    liability_status=source_insurance.liability_status,
                    liability_percentage=source_insurance.liability_percentage,
                    coverage_status=source_insurance.coverage_status,
                    med_pay=source_insurance.med_pay,
                    confirmed=source_insurance.confirmed,
                    claim_note=source_insurance.claim_note,
                    insurance_company=source_insurance.insurance_company,
                )

                # Create sync relationship
                _, sync_group = self.get_or_create_sync_relationship(source_insurance, target_insurance, content_type)
                print(f"Created/updated sync relationship between {source_insurance.id} and {target_insurance.id}")
            else:
                # Update the target insurance with source data
                target_insurance.no_insurance = source_insurance.no_insurance
                target_insurance.umbrella_policy = source_insurance.umbrella_policy
                target_insurance.claim_number = source_insurance.claim_number
                target_insurance.policy_number = source_insurance.policy_number
                target_insurance.insured = source_insurance.insured
                target_insurance.policy_limits = source_insurance.policy_limits
                target_insurance.liability_status = source_insurance.liability_status
                target_insurance.liability_percentage = source_insurance.liability_percentage
                target_insurance.coverage_status = source_insurance.coverage_status
                target_insurance.med_pay = source_insurance.med_pay
                target_insurance.confirmed = source_insurance.confirmed
                target_insurance.claim_note = source_insurance.claim_note
                target_insurance.insurance_company = source_insurance.insurance_company
                target_insurance.save()
                print(f"Updated existing insurance {target_insurance.id}")

            synced_ids.add(target_insurance.id)

            # Handle nested relations
            if sync_adjusters:
                self._sync_adjusters(source_insurance, target_insurance)

    def _sync_property_damage(self, source_defendant, target_defendant):
        """Sync property damage from source to target defendant"""
        print(f"\n=== Syncing property damage for defendant {source_defendant.id} ===")

        try:
            # Get source property damage
            source_damage = DefendantPropertyDamage.objects.get(defendant=source_defendant)

            # Get ContentType
            content_type = ContentType.objects.get_for_model(DefendantPropertyDamage)

            # Check if this property damage already has synced objects linked to the target defendant
            synced_objects = self.get_all_synced_objects(source_damage, content_type)

            # Try to find a target property damage in the synced objects
            target_damage = None
            for obj_id, obj in synced_objects.items():
                target_defendant_property_damage = DefendantPropertyDamage.objects.get(defendant=target_defendant)
                if obj.target_object_id == target_defendant_property_damage.id:
                    target_damage = target_defendant_property_damage
                    print(f"Found existing synced property damage {target_damage.id} through transitive relationship")
                    break

            # If no existing relationship found through transitive relationships
            if not target_damage:
                # Look for existing or create new one
                target_damage, created = DefendantPropertyDamage.objects.get_or_create(
                    defendant=target_defendant,
                    defaults={
                        "make": source_damage.make,
                        "model": source_damage.model,
                        "color": source_damage.color,
                        "year": source_damage.year,
                        "plate": source_damage.plate,
                        "vin": source_damage.vin,
                        "mileage": source_damage.mileage,
                        "damage": source_damage.damage,
                        "frame_damage": source_damage.frame_damage,
                        "total_loss": source_damage.total_loss,
                        "registered_owner": source_damage.registered_owner,
                        "auto_body_shop": source_damage.auto_body_shop,
                        "estimate": source_damage.estimate,
                        "final": source_damage.final,
                        "note": source_damage.note,
                    },
                )

                if created:
                    print(f"Created new property damage for target defendant {target_defendant.id}")
                else:
                    print(f"Found existing property damage {target_damage.id} for target defendant")
                    # Update fields
                    target_damage.make = source_damage.make
                    target_damage.model = source_damage.model
                    target_damage.color = source_damage.color
                    target_damage.year = source_damage.year
                    target_damage.plate = source_damage.plate
                    target_damage.vin = source_damage.vin
                    target_damage.mileage = source_damage.mileage
                    target_damage.damage = source_damage.damage
                    target_damage.frame_damage = source_damage.frame_damage
                    target_damage.total_loss = source_damage.total_loss
                    target_damage.registered_owner = source_damage.registered_owner
                    target_damage.auto_body_shop = source_damage.auto_body_shop
                    target_damage.estimate = source_damage.estimate
                    target_damage.final = source_damage.final
                    target_damage.note = source_damage.note
                    target_damage.save()

                # Create sync relationship
                _, sync_group = self.get_or_create_sync_relationship(source_damage, target_damage, content_type)
                print(f"Created/updated sync relationship between {source_damage.id} and {target_damage.id}")
            else:
                # Update the target property damage with source data
                target_damage.make = source_damage.make
                target_damage.model = source_damage.model
                target_damage.color = source_damage.color
                target_damage.year = source_damage.year
                target_damage.plate = source_damage.plate
                target_damage.vin = source_damage.vin
                target_damage.mileage = source_damage.mileage
                target_damage.damage = source_damage.damage
                target_damage.frame_damage = source_damage.frame_damage
                target_damage.total_loss = source_damage.total_loss
                target_damage.registered_owner = source_damage.registered_owner
                target_damage.auto_body_shop = source_damage.auto_body_shop
                target_damage.estimate = source_damage.estimate
                target_damage.final = source_damage.final
                target_damage.note = source_damage.note
                target_damage.save()
                print(f"Updated existing property damage {target_damage.id}")

        except DefendantPropertyDamage.DoesNotExist:
            print("No property damage found for source defendant")

    def _sync_adjusters(self, source_insurance, target_insurance):
        """Sync adjusters from source to target insurance"""
        print(f"\n=== Syncing adjusters for insurance {source_insurance.id} ===")

        try:
            # Get source adjusters
            source_adjuster = DefendantInsuranceAdjuster.objects.get(defendant_insurance=source_insurance)

            # Check if we already have sync relationships using get_all_synced_objects
            content_type = ContentType.objects.get_for_model(DefendantInsuranceAdjuster)
            synced_objects = self.get_all_synced_objects(source_adjuster, content_type)

            # Try to find a target adjuster in the synced objects
            target_adjuster = None
            for obj_id, obj in synced_objects.items():
                if obj.target_object_id == target_insurance.adjusters.id:
                    target_adjuster = DefendantInsuranceAdjuster.objects.get(id=obj.target_object_id)
                    print(f"Found existing synced adjuster {target_adjuster.id} through transitive relationship")
                    break

            # If no existing relationship found through transitive relationships
            """

            """
            if not target_adjuster:
                # Look for existing or create new one
                target_adjuster, created = DefendantInsuranceAdjuster.objects.get_or_create(
                    defendant_insurance=target_insurance,
                    defaults={
                        "bodily_injury": source_adjuster.bodily_injury,
                        "bi_supervisor": source_adjuster.bi_supervisor,
                        "medpay_pip": source_adjuster.medpay_pip,
                        "medpay_pip_supervisor": source_adjuster.medpay_pip_supervisor,
                        "property_damage": source_adjuster.property_damage,
                        "pd_supervisor": source_adjuster.pd_supervisor,
                    },
                )

                if created:
                    print(f"Created new adjuster for target insurance {target_insurance.id}")
                else:
                    print(f"Found existing adjuster {target_adjuster.id} for target insurance")

                # Update fields
                target_adjuster.bodily_injury = source_adjuster.bodily_injury
                target_adjuster.bi_supervisor = source_adjuster.bi_supervisor
                target_adjuster.medpay_pip = source_adjuster.medpay_pip
                target_adjuster.medpay_pip_supervisor = source_adjuster.medpay_pip_supervisor
                target_adjuster.property_damage = source_adjuster.property_damage
                target_adjuster.pd_supervisor = source_adjuster.pd_supervisor
                target_adjuster.save()

                # Create sync relationship using the new method
                _, sync_group = self.get_or_create_sync_relationship(source_adjuster, target_adjuster, content_type)
                print(f"Created/updated sync relationship between {source_adjuster.id} and {target_adjuster.id}")
            else:
                # Update the target adjuster with source data
                target_adjuster.defendant_insurance = target_insurance
                target_adjuster.bodily_injury = source_adjuster.bodily_injury
                target_adjuster.bi_supervisor = source_adjuster.bi_supervisor
                target_adjuster.medpay_pip = source_adjuster.medpay_pip
                target_adjuster.medpay_pip_supervisor = source_adjuster.medpay_pip_supervisor
                target_adjuster.property_damage = source_adjuster.property_damage
                target_adjuster.pd_supervisor = source_adjuster.pd_supervisor
                target_adjuster.save()
                print(f"Updated existing adjuster {target_adjuster.id}")

        except DefendantInsuranceAdjuster.DoesNotExist:
            print(f"No adjusters found for source insurance {source_insurance.id}")

    def _sync_legal_representation(self, source_defendant, target_defendant):
        """Sync legal representation from source to target defendant"""
        print(f"\n=== Syncing legal representation for defendant {source_defendant.id} ===")

        try:
            # Get source legal representation
            try:
                source_legal = CaseDefendantLegalRepresentation.objects.get(defendant=source_defendant)

                # Check if we already have sync relationships using get_all_synced_objects
                content_type = ContentType.objects.get_for_model(CaseDefendantLegalRepresentation)
                synced_objects = self.get_all_synced_objects(source_legal, content_type)

                # Try to find a target legal representation in the synced objects
                target_legal = None
                for obj_id, obj in synced_objects.items():
                    if obj.target_object_id == target_defendant.legal_representation.id:
                        target_legal = CaseDefendantLegalRepresentation.objects.get(id=obj.target_object_id)
                        print(
                            f"Found existing synced legal representation {target_legal.id} through transitive relationship"
                        )
                        break

                # If no existing relationship found through transitive relationships
                if not target_legal:
                    # Look for existing or create new one
                    target_legal, created = CaseDefendantLegalRepresentation.objects.get_or_create(
                        defendant=target_defendant,
                        defaults={
                            "law_firm": source_legal.law_firm,
                            "attorney": source_legal.attorney,
                            "co_counsel_law_firm": source_legal.co_counsel_law_firm,
                            "co_counsel_attorney": source_legal.co_counsel_attorney,
                            "legal_note": source_legal.legal_note,
                        },
                    )

                    if created:
                        print(f"Created new legal representation for target defendant {target_defendant.id}")
                    else:
                        print(f"Found existing legal representation {target_legal.id} for target defendant")
                        # Update fields
                        target_legal.law_firm = source_legal.law_firm
                        target_legal.attorney = source_legal.attorney
                        target_legal.co_counsel_law_firm = source_legal.co_counsel_law_firm
                        target_legal.co_counsel_attorney = source_legal.co_counsel_attorney
                        target_legal.legal_note = source_legal.legal_note
                        target_legal.save()

                    # Create sync relationship using the new method
                    _, sync_group = self.get_or_create_sync_relationship(source_legal, target_legal, content_type)
                    print(f"Created/updated sync relationship between {source_legal.id} and {target_legal.id}")
                else:
                    # Update existing target legal representation
                    target_legal.law_firm = source_legal.law_firm
                    target_legal.attorney = source_legal.attorney
                    target_legal.co_counsel_law_firm = source_legal.co_counsel_law_firm
                    target_legal.co_counsel_attorney = source_legal.co_counsel_attorney
                    target_legal.legal_note = source_legal.legal_note
                    target_legal.save()
                    print(f"Updated existing legal representation {target_legal.id}")

            except CaseDefendantLegalRepresentation.DoesNotExist:
                print("No legal representation found for source defendant")

        except Exception as e:
            print(f"Error syncing legal representation: {str(e)}")


class CaseNoteSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """
    ViewSet for syncing notes between cases.
    Simplified to provide a single sync API endpoint.
    """

    def get_serializer_class(self):
        return NoteSyncSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync a note from one case to multiple target cases.

        Expected payload:
        {
            "source_note_id": 123,
            "target_case_ids": [456, 789]
        }
        """
        print(f"\n=== Starting Note Sync Process for case {case_id} ===")

        # Validate input
        source_note_id = request.data.get("source_note_id")
        case = Case.objects.get(id=case_id)
        target_case_ids = request.data.get("target_case_ids", [])

        if not source_note_id:
            return Response({"error": "source_note_id is required"}, status=400)

        if not target_case_ids:
            return Response({"error": "target_case_ids is required"}, status=400)

        try:
            source_note = CaseNote.objects.get(id=source_note_id, case_id=case_id)

            # Get target cases
            target_cases = Case.objects.filter(id__in=target_case_ids)

            if not target_cases.exists():
                return Response({"error": "No valid target cases found"}, status=400)

            synced_targets = []
            errors = []

            for target_case in target_cases:
                self._sync_note(source_note, target_case)
                synced_targets.append(target_case.id)

            return Response(
                {
                    "message": "Note sync completed",
                    "synced_cases": synced_targets,
                    "errors": errors if errors else None,
                }
            )
        except Exception as e:
            print(f"Error in note sync process: {str(e)}")
            return Response({"error": f"Failed to sync note: {str(e)}"}, status=400)

    def _sync_note(self, source_note, target_case):
        with transaction.atomic():
            content_type = ContentType.objects.get_for_model(CaseNote)
            synced_objects_dict = self.get_all_synced_objects(source_note, content_type)
            print(f"Synced objects for note {source_note.id}: {synced_objects_dict}")
            target_note = None
            for obj_id, obj in synced_objects_dict.items():
                for obj_id, obj in synced_objects_dict.items():
                    if obj.target_object_id == CaseNote.objects.filter(case=target_case).values_list("id", flat=True):
                        target_note = CaseNote.objects.get(id=obj.target_object_id)
                        print(
                            f"Found existing synced note {target_note.id} in target case through transitive relationship"
                        )
                        break
        if not target_note:
            target_note = CaseNote.objects.create(
                case=target_case,
                title=source_note.title,
                content=source_note.content,
                created_by=source_note.created_by,
                is_editable=source_note.is_editable,
            )
            print(f"Created new note {target_note.id} in target case {target_case.id}")
        else:
            print(f"Updating existing note {target_note.id} in target case {target_case.id}")
            # Update existing note
            target_note.title = source_note.title
            target_note.content = source_note.content
            target_note.is_editable = source_note.is_editable
            target_note.save()
            print(f"Updated existing note {target_note.id} in target case {target_case.id}")

        # Add tagged users if they exist in the organization
        target_note.tagged_users.clear()
        print(f"Clearing tagged users for note {target_note.id}")
        for user in source_note.tagged_users.all():
            if user.organizations.filter(id=target_case.organization.id).exists():
                target_note.tagged_users.add(user)

        # Add organization tags if they exist
        target_note.tags.clear()
        print(f"Clearing tags for note {target_note.id}")
        for tag in source_note.tags.all():
            if tag.organization_id == target_case.organization.id:
                target_note.tags.add(tag)

        # Create sync relationship using get_or_create_sync_relationship
        _, sync_group = self.get_or_create_sync_relationship(source_note, target_note, content_type)
        print(f"Created/updated sync relationship between {source_note.id} and {target_note.id}")


class TaskSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """
    ViewSet for syncing tasks between cases.
    Simplified to provide a single sync API endpoint.
    """

    def get_serializer_class(self):
        return TaskSyncSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync a task from one case to multiple target cases.

        Expected payload:
        {
            "source_task_id": 123,
            "target_case_ids": [456, 789]
        }
        """
        print("\n=== Starting Task Sync Process ===")

        # Validate input
        source_task_id = request.data.get("source_task_id")
        source_task = Task.objects.get(id=source_task_id, case_id=case_id)
        case = Case.objects.get(id=case_id)
        target_case_ids = request.data.get("target_case_ids", [])

        if not source_task_id:
            return Response({"error": "source_task_id is required"}, status=400)

        if not target_case_ids:
            return Response({"error": "target_case_ids is required"}, status=400)

        try:
            # Get source task
            source_task = Task.objects.get(id=source_task_id, case_id=case_id)

            # Get target cases
            target_cases = Case.objects.filter(id__in=target_case_ids)

            if not target_cases.exists():
                return Response({"error": "No valid target cases found"}, status=400)

            synced_objects = []
            errors = []

            with transaction.atomic():
                for target_case in target_cases:
                    if case_id == target_case.id:
                        continue
                    try:
                        print(f"\nSyncing to target case: {target_case.id}")

                        # Check if we already have a sync relationship using get_all_synced_objects
                        content_type = ContentType.objects.get_for_model(Task)
                        synced_objects_dict = self.get_all_synced_objects(source_task, content_type)

                        # Try to find a target task in the synced objects that belongs to the target case
                        target_task = None
                        for obj_id, obj in synced_objects_dict.items():
                            if obj.target_object_id == Task.objects.filter(case=target_case).values_list(
                                "id", flat=True
                            ):
                                target_task = Task.objects.get(id=obj.target_object_id)
                                print(
                                    f"Found existing synced task {target_task.id} in target case through transitive relationship"
                                )
                                break

                        # If no existing task found through transitive relationships, create a new one
                        if not target_task:
                            # Create target task
                            target_task = Task.objects.create(
                                case=target_case,
                                title=source_task.title,
                                description=source_task.description,
                                status=source_task.status,
                                priority=source_task.priority,
                                due_date=source_task.due_date,
                                created_by=request.user,
                                is_overdue=source_task.is_overdue,
                                overwritten_by_admin=False,  # Reset in new case
                            )
                            print(f"Created new task {target_task.id} in target case {target_case.id}")
                        else:
                            # Update existing task
                            target_task.title = source_task.title
                            target_task.description = source_task.description
                            target_task.status = source_task.status
                            target_task.priority = source_task.priority
                            target_task.due_date = source_task.due_date
                            target_task.is_overdue = source_task.is_overdue
                            target_task.save()
                            print(f"Updated existing task {target_task.id} in target case {target_case.id}")

                        # Check if assigned_to user exists in target organization and update them
                        if source_task.assigned_to:
                            target_task.assigned_to = source_task.assigned_to
                            target_task.save(update_fields=["assigned_to"])

                        # Add tagged users if they exist in the organization
                        target_task.tagged_users.clear()
                        for user in source_task.tagged_users.all():
                            target_task.tagged_users.add(user)

                        # Create sync relationship using get_or_create_sync_relationship
                        _, sync_group = self.get_or_create_sync_relationship(source_task, target_task, content_type)
                        print(f"Created/updated sync relationship between {source_task.id} and {target_task.id}")

                        synced_objects.append(target_case.id)

                    except Exception as e:
                        print(f"Error syncing to case {target_case.id}: {str(e)}")
                        errors.append({"case_id": target_case.id, "error": str(e)})
                        continue

            return Response(
                {
                    "message": "Task sync completed",
                    "synced_cases": [obj for obj in synced_objects],
                    "errors": errors if errors else None,
                }
            )

        except Task.DoesNotExist:
            return Response({"error": "Source task not found"}, status=404)
        except Exception as e:
            print(f"Error in task sync process: {str(e)}")
            return Response({"error": f"Failed to sync task: {str(e)}"}, status=400)


class UserCardSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """
    ViewSet for syncing user cards (case highlights) between cases.
    Simplified to provide a single sync API endpoint.
    """

    def get_serializer_class(self):
        return UserCardSyncSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync a user card (case highlight) from one case to multiple target cases.

        Expected payload:
        {
            "source_card_id": 123,
            "target_case_ids": [456, 789]
        }
        """
        print("\n=== Starting User Card Sync Process ===")

        # Validate input
        source_card_id = request.data.get("source_card_id")
        source_card = UserCard.objects.get(id=source_card_id, case_id=case_id)
        case = Case.objects.get(id=case_id)
        target_case_ids = request.data.get("target_case_ids", [])

        if not source_card_id:
            return Response({"error": "source_card_id is required"}, status=400)

        if not target_case_ids:
            return Response({"error": "target_case_ids is required"}, status=400)

        try:
            # Get source card
            source_card = UserCard.objects.get(id=source_card_id)

            # Verify the card belongs to the source case
            if str(source_card.case_id) != case_id:
                return Response({"error": "Source card does not belong to the specified case"}, status=400)

            # Get target cases
            target_cases = Case.objects.filter(id__in=target_case_ids)

            if not target_cases.exists():
                return Response({"error": "No valid target cases found"}, status=400)

            synced_objects = []
            errors = []

            with transaction.atomic():
                for target_case in target_cases:
                    if case_id == target_case.id:
                        continue
                    try:
                        print(f"\nSyncing to target case: {target_case.id}")

                        # Check if we already have a sync relationship using get_all_synced_objects
                        content_type = ContentType.objects.get_for_model(UserCard)
                        synced_objects_dict = self.get_all_synced_objects(source_card, content_type)

                        # Try to find a target card in the synced objects that belongs to the target case
                        target_card = None
                        for obj_id, obj in synced_objects_dict.items():
                            if obj.target_object_id in UserCard.objects.filter(case=target_case).values_list(
                                "id", flat=True
                            ):
                                target_card = UserCard.objects.get(id=obj.target_object_id)
                        # If still no card found, create a new one
                        if not target_card:
                            target_card = UserCard.objects.filter(case=target_case).first()
                        if not target_card:
                            # Create target card
                            target_card = UserCard.objects.create(
                                case=target_case,
                                title=source_card.title,
                                description=source_card.description,
                                content=source_card.content,
                                is_pinned=source_card.is_pinned,
                                color=source_card.color,
                                order=source_card.order,
                                is_visible=source_card.is_visible,
                                created_by=source_card.created_by,
                            )
                            print(f"Created new card {target_card.id} in target case {target_case.id}")
                        else:
                            # Update existing card
                            target_card.title = source_card.title
                            target_card.description = source_card.description
                            target_card.content = source_card.content
                            target_card.is_pinned = source_card.is_pinned
                            target_card.color = source_card.color
                            target_card.order = source_card.order
                            target_card.is_visible = source_card.is_visible
                            target_card.created_by = source_card.created_by
                            target_card.save()
                            print(f"Updated existing card {target_card.id} in target case {target_case.id}")

                        # Create sync relationship using get_or_create_sync_relationship
                        _, sync_group = self.get_or_create_sync_relationship(source_card, target_card, content_type)
                        print(f"Created/updated sync relationship between {source_card.id} and {target_card.id}")

                        synced_objects.append(target_case.id)

                    except Exception as e:
                        print(f"Error syncing to case {target_case.id}: {str(e)}")
                        errors.append({"case_id": target_case.id, "error": str(e)})
                        continue

                return Response(
                    {
                        "message": "User card sync completed",
                        "synced_cases": [obj for obj in synced_objects],
                        "errors": errors if errors else None,
                    }
                )

        except UserCard.DoesNotExist:
            return Response({"error": "Source user card not found"}, status=404)
        except Exception as e:
            print(f"Error in user card sync process: {str(e)}")
            return Response({"error": f"Failed to sync user card: {str(e)}"}, status=400)


class ClientInsuranceSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """
    ViewSet for syncing client insurances between cases.
    """

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync a specific insurance from one case to multiple target cases.
        """
        print("\n=== Starting Insurance Sync Process ===")

        # Validate input
        source_insurance_id = request.data.get("source_insurance_id")
        target_case_ids = request.data.get("target_case_ids", [])
        sync_options = request.data.get("sync_options", {})

        if not source_insurance_id:
            return Response({"error": "source_insurance_id is required"}, status=400)

        if not target_case_ids:
            return Response({"error": "target_case_ids is required"}, status=400)

        try:
            # Get source insurance
            source_insurance = ClientInsurance.objects.get(id=source_insurance_id)
            source_case = source_insurance.case

            # Get target cases
            target_cases = Case.objects.filter(id__in=target_case_ids)

            if not target_cases.exists():
                return Response({"error": "No valid target cases found"}, status=400)

            synced_objects = []
            errors = []

            for target_case in target_cases:
                if source_case.id == target_case.id:
                    continue
                try:
                    print(f"\nSyncing to target case: {target_case.id}")

                    # Sync the insurance and its related data
                    self._sync_insurances(
                        source_insurance,
                        target_case,
                    )

                    # Sync property damage if requested
                    if sync_options.get("sync_property_damage", False):
                        self._sync_property_damage(source_case, target_case)

                    synced_objects.append(target_case.id)

                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}

                    print(f"Error syncing to case {target_case.id}: {str(e)}")
                    errors.append({"case_id": target_case.id, "error": error_details})
                    continue

            return Response(
                {
                    "message": "Insurance sync completed",
                    "synced_cases": synced_objects,
                    "errors": errors if errors else None,
                }
            )

        except ClientInsurance.DoesNotExist:
            return Response({"error": "Source insurance not found"}, status=404)
        except Exception as e:
            print(f"Error in sync process: {str(e)}")
            return Response({"error": f"Failed to sync insurance: {str(e)}"}, status=400)

    def _sync_insurances(self, source_insurance, target_case, sync_adjusters=True, sync_legal_representation=True):
        """Sync a single insurance from source to target case, including adjusters and legal representation"""
        print(f"\n=== Syncing insurance {source_insurance.id} to case {target_case.id} ===")

        # Get ContentType for ClientInsurance
        content_type = ContentType.objects.get_for_model(ClientInsurance)

        # Check if this insurance already has synced objects in the target case
        synced_objects = self.get_all_synced_objects(source_insurance, content_type)

        # Try to find a target insurance in the synced objects that belongs to the target case
        target_insurance = None
        for obj_id, obj in synced_objects.items():
            if obj.target_object_id in ClientInsurance.objects.filter(case=target_case).values_list("id", flat=True):
                target_insurance = ClientInsurance.objects.get(id=obj.target_object_id)
                print(
                    f"Found existing synced insurance {target_insurance.id} in target case through transitive relationship"
                )
                break

        # If no matching insurance found through transitive relationships
        if not target_insurance:
            print("Creating new insurance in target case")
            target_insurance = ClientInsurance.objects.create(
                case=target_case,
                insurance_company=source_insurance.insurance_company,
                no_insurance=source_insurance.no_insurance,
                plan_type=source_insurance.plan_type,
                claim_number=source_insurance.claim_number,
                policy_number=source_insurance.policy_number,
                medpay_claim_number=source_insurance.medpay_claim_number,
                insured_name=source_insurance.insured_name,
                um_uim=source_insurance.um_uim,
                medpay=source_insurance.medpay,
                pip=source_insurance.pip,
                deductible=source_insurance.deductible,
                coverage_status=source_insurance.coverage_status,
                liability_status=source_insurance.liability_status,
                liability_type=source_insurance.liability_type,
                policy_type=source_insurance.policy_type,
                stacked=source_insurance.stacked,
                vehicles=source_insurance.vehicles,
                claim_note=source_insurance.claim_note,
            )
        else:
            target_insurance.insurance_company = source_insurance.insurance_company
            target_insurance.no_insurance = source_insurance.no_insurance
            target_insurance.plan_type = source_insurance.plan_type
            target_insurance.claim_number = source_insurance.claim_number
            target_insurance.policy_number = source_insurance.policy_number
            target_insurance.medpay_claim_number = source_insurance.medpay_claim_number
            target_insurance.insured_name = source_insurance.insured_name
            target_insurance.um_uim = source_insurance.um_uim
            target_insurance.medpay = source_insurance.medpay
            target_insurance.pip = source_insurance.pip
            target_insurance.deductible = source_insurance.deductible
            target_insurance.coverage_status = source_insurance.coverage_status
            target_insurance.liability_status = source_insurance.liability_status
            target_insurance.liability_type = source_insurance.liability_type
            target_insurance.policy_type = source_insurance.policy_type
            target_insurance.stacked = source_insurance.stacked
            target_insurance.vehicles = source_insurance.vehicles
            target_insurance.claim_note = source_insurance.claim_note
            target_insurance.save()

            # Create new sync relationship using the method
            _, sync_group = self.get_or_create_sync_relationship(source_insurance, target_insurance, content_type)
            print(f"Created/updated sync relationship between {source_insurance.id} and {target_insurance.id}")

        # Sync related objects
        if sync_adjusters:
            self._sync_insurance_adjusters(source_insurance, target_insurance)

        if sync_legal_representation:
            self._sync_legal_representation(source_insurance, target_insurance)

        print(f"Synced insurance {source_insurance.id} to case {target_case.id}")

    def _sync_insurance_adjusters(self, source_insurance, target_insurance):
        """Sync insurance adjusters from source to target insurance"""
        print(f"\n=== Syncing adjusters for insurance {source_insurance.id} ===")

        try:
            # Get source adjusters
            source_adjusters = InsuranceAdjuster.objects.get(client_insurance=source_insurance)

            # Check if we already have sync relationships using get_all_synced_objects
            content_type = ContentType.objects.get_for_model(InsuranceAdjuster)
            synced_objects = self.get_all_synced_objects(source_adjusters, content_type)

            # Try to find a target adjuster in the synced objects
            target_adjusters = None
            for obj_id, obj in synced_objects.items():
                if obj.target_object_id in InsuranceAdjuster.objects.filter(
                    client_insurance=target_insurance
                ).values_list("id", flat=True):
                    target_adjusters = InsuranceAdjuster.objects.get(id=obj.target_object_id)
                    print(f"Found existing synced adjuster {target_adjusters.id} through transitive relationship")
                    break

            # If no existing relationship found through transitive relationships
            if not target_adjusters:
                # Look for existing or create new one
                target_adjusters, created = InsuranceAdjuster.objects.get_or_create(client_insurance=target_insurance)
                if created:
                    print(f"Created new adjuster for target insurance {target_insurance.id}")
                else:
                    print(f"Found existing adjuster {target_adjusters.id} for target insurance")

                # Update fields
                target_adjusters.bodily_injury = source_adjusters.bodily_injury
                target_adjusters.bi_supervisor = source_adjusters.bi_supervisor
                target_adjusters.medpay_pip = source_adjusters.medpay_pip
                target_adjusters.medpay_pip_supervisor = source_adjusters.medpay_pip_supervisor
                target_adjusters.property_damage = source_adjusters.property_damage
                target_adjusters.pd_supervisor = source_adjusters.pd_supervisor
                target_adjusters.save()

                # Create sync relationship using the new method
                _, sync_group = self.get_or_create_sync_relationship(source_adjusters, target_adjusters, content_type)
                print(f"Created/updated sync relationship between {source_adjusters.id} and {target_adjusters.id}")
            else:
                # Update the target adjuster with source data
                target_adjusters.client_insurance = target_insurance
                target_adjusters.bodily_injury = source_adjusters.bodily_injury
                target_adjusters.bi_supervisor = source_adjusters.bi_supervisor
                target_adjusters.medpay_pip = source_adjusters.medpay_pip
                target_adjusters.medpay_pip_supervisor = source_adjusters.medpay_pip_supervisor
                target_adjusters.property_damage = source_adjusters.property_damage
                target_adjusters.pd_supervisor = source_adjusters.pd_supervisor
                target_adjusters.save()
                print(f"Updated existing adjuster {target_adjusters.id}")

        except InsuranceAdjuster.DoesNotExist:
            print(f"No adjusters found for source insurance {source_insurance.id}")

    def _sync_legal_representation(self, source_insurance, target_insurance):
        """Sync legal representation from source to target insurance"""
        print(f"\n=== Syncing legal representation for insurance {source_insurance.id} ===")

        try:
            # Get source legal representation
            source_legal = InsuranceLegalRepresentation.objects.get(client_insurance=source_insurance)

            # Check if we already have a sync relationship using get_all_synced_objects
            content_type = ContentType.objects.get_for_model(InsuranceLegalRepresentation)
            synced_objects = self.get_all_synced_objects(source_legal, content_type)

            # Try to find a target legal representation in the synced objects
            target_legal = None
            for obj_id, obj in synced_objects.items():
                if obj.target_object_id in InsuranceLegalRepresentation.objects.filter(
                    client_insurance=target_insurance
                ).values_list("id", flat=True):
                    target_legal = InsuranceLegalRepresentation.objects.get(id=obj.target_object_id)
                    print(
                        f"Found existing synced legal representation {target_legal.id} through transitive relationship"
                    )
                    break

            # If no existing relationship found through transitive relationships
            if not target_legal:
                # Look for an existing object or create new one
                target_legal, created = InsuranceLegalRepresentation.objects.get_or_create(
                    client_insurance=target_insurance,
                )
                if created:
                    print(f"Created new legal representation for target insurance {target_insurance.id}")
                else:
                    print(f"Found existing legal representation {target_legal.id} for target insurance")

                # Update fields - using correct InsuranceLegalRepresentation fields
                target_legal.law_firm = source_legal.law_firm
                target_legal.attorney = source_legal.attorney
                target_legal.co_counsel_law_firm = source_legal.co_counsel_law_firm
                target_legal.co_counsel_attorney = source_legal.co_counsel_attorney
                target_legal.legal_note = source_legal.legal_note
                target_legal.save()

                # Create sync relationship using the new method
                _, sync_group = self.get_or_create_sync_relationship(source_legal, target_legal, content_type)
                print(f"Created/updated sync relationship between {source_legal.id} and {target_legal.id}")
            else:
                # Update the target legal representation with source data
                target_legal.client_insurance = target_insurance
                target_legal.law_firm = source_legal.law_firm
                target_legal.attorney = source_legal.attorney
                target_legal.co_counsel_law_firm = source_legal.co_counsel_law_firm
                target_legal.co_counsel_attorney = source_legal.co_counsel_attorney
                target_legal.legal_note = source_legal.legal_note
                target_legal.save()
                print(f"Updated existing legal representation {target_legal.id}")

        except InsuranceLegalRepresentation.DoesNotExist:
            print(f"No legal representation found for source insurance {source_insurance.id}")

    def _sync_property_damage(self, source_case, target_case):
        """Sync property damage from source to target case"""
        print(f"\n=== Syncing property damage from case {source_case.id} to {target_case.id} ===")

        try:
            # Get source property damage
            source_property_damage = ClientPropertyDamage.objects.get(case=source_case)

            # Get ContentType for ClientPropertyDamage
            content_type = ContentType.objects.get_for_model(ClientPropertyDamage)

            # Check if we already have a sync relationship
            synced_objects = self.get_all_synced_objects(source_property_damage, content_type)

            # Try to find a target property damage in the synced objects
            target_property_damage = None
            for obj_id, obj in synced_objects.items():
                if obj.target_object_id in ClientPropertyDamage.objects.filter(case=target_case).values_list(
                    "id", flat=True
                ):
                    target_property_damage = ClientPropertyDamage.objects.get(id=obj.target_object_id)
                    break

            if not target_property_damage:
                # Check if target case already has property damage
                target_property_damage = ClientPropertyDamage.objects.filter(case=target_case).first()
                if target_property_damage:
                    print(f"Found existing property damage {target_property_damage.id} in target case")
                else:
                    # Create new property damage
                    target_property_damage = ClientPropertyDamage.objects.create(
                        case=target_case,
                        vehicle_make=source_property_damage.vehicle_make,
                        vehicle_model=source_property_damage.vehicle_model,
                        vehicle_year=source_property_damage.vehicle_year,
                        vehicle_vin=source_property_damage.vehicle_vin,
                        license_plate=source_property_damage.license_plate,
                        mileage=source_property_damage.mileage,
                        color=source_property_damage.color,
                        damage=source_property_damage.damage,
                        frame_damage=source_property_damage.frame_damage,
                        total_loss=source_property_damage.total_loss,
                        damage_description=source_property_damage.damage_description,
                        registered_owner=source_property_damage.registered_owner,
                        auto_body_shop=source_property_damage.auto_body_shop,
                        estimate=source_property_damage.estimate,
                        final=source_property_damage.final,
                        insurance_company=source_property_damage.insurance_company,
                        note=source_property_damage.note,
                    )
                    print(f"Created new property damage for target case {target_case.id}")

                # Create sync relationship
                _, sync_group = self.get_or_create_sync_relationship(
                    source_property_damage, target_property_damage, content_type
                )
                print(
                    f"Created/updated sync relationship between {source_property_damage.id} and {target_property_damage.id}"
                )
            else:
                # Update existing property damage
                target_property_damage.vehicle_make = source_property_damage.vehicle_make
                target_property_damage.vehicle_model = source_property_damage.vehicle_model
                target_property_damage.vehicle_year = source_property_damage.vehicle_year
                target_property_damage.vehicle_vin = source_property_damage.vehicle_vin
                target_property_damage.license_plate = source_property_damage.license_plate
                target_property_damage.mileage = source_property_damage.mileage
                target_property_damage.color = source_property_damage.color
                target_property_damage.damage = source_property_damage.damage
                target_property_damage.frame_damage = source_property_damage.frame_damage
                target_property_damage.total_loss = source_property_damage.total_loss
                target_property_damage.damage_description = source_property_damage.damage_description
                target_property_damage.registered_owner = source_property_damage.registered_owner
                target_property_damage.auto_body_shop = source_property_damage.auto_body_shop
                target_property_damage.estimate = source_property_damage.estimate
                target_property_damage.final = source_property_damage.final
                target_property_damage.insurance_company = source_property_damage.insurance_company
                target_property_damage.note = source_property_damage.note
                target_property_damage.save()
                print(f"Updated existing property damage {target_property_damage.id}")

        except ClientPropertyDamage.DoesNotExist:
            print(f"No property damage found for source case {source_case.id}")
        except Exception as e:
            print(f"Error syncing property damage: {str(e)}")
            raise


class CaseIncidentDetailsSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync incident details between cases"""

    def get_serializer_class(self):
        return CaseIncidentDetailsSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync incident details from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_incident_details(source_case, target_case)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}
                    errors.append({"case_id": target_case.id, "error": str(error_details)})

        return Response(
            {"message": "Incident details synced successfully", "errors": errors, "synced_cases": synced_objects}
        )

    def _sync_incident_details(self, source_case, target_case):
        """Sync incident details between cases"""
        source_details = source_case.incident_details
        if not source_details:
            return

        # Handle incident details sync
        content_type = ContentType.objects.get_for_model(CaseIncidentDetails)
        synced_objects_dict = self.get_all_synced_objects(source_details, content_type)

        target_details = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id in CaseIncidentDetails.objects.filter(case=target_case).values_list(
                "id", flat=True
            ):
                target_details = CaseIncidentDetails.objects.get(id=obj.target_object_id)
                break

        if not target_details:
            # Create new incident details
            target_details = CaseIncidentDetails.objects.create(
                case=target_case,
                incident_date=source_details.incident_date,
                incident_type=source_details.incident_type,
                incident_location=source_details.incident_location,
                insurance_status=source_details.insurance_status,
                incident_description=source_details.incident_description,
                weather_conditions=source_details.weather_conditions,
                police_report_details=source_details.police_report_details,
                witness_information=source_details.witness_information,
                previous_attorney=source_details.previous_attorney,
                basic_facts=source_details.basic_facts,
                report_number=source_details.report_number,
                doc_request_status=source_details.doc_request_status,
                doc_request_notes=source_details.doc_request_notes,
                estimated_value=source_details.estimated_value,
                statute_of_limitations=source_details.statute_of_limitations,
                street1=source_details.street1,
                street2=source_details.street2,
                city=source_details.city,
                county=source_details.county,
                state=source_details.state,
                zip_code=source_details.zip_code,
                requested_at=source_details.requested_at,
                requested_by=source_details.requested_by,
                received_at=source_details.received_at,
                received_by=source_details.received_by,
            )
        else:
            # Update existing incident details
            target_details.incident_date = source_details.incident_date
            target_details.incident_type = source_details.incident_type
            target_details.incident_location = source_details.incident_location
            target_details.insurance_status = source_details.insurance_status
            target_details.incident_description = source_details.incident_description
            target_details.weather_conditions = source_details.weather_conditions
            target_details.police_report_details = source_details.police_report_details
            target_details.witness_information = source_details.witness_information
            target_details.previous_attorney = source_details.previous_attorney
            target_details.basic_facts = source_details.basic_facts
            target_details.report_number = source_details.report_number
            target_details.doc_request_status = source_details.doc_request_status
            target_details.doc_request_notes = source_details.doc_request_notes
            target_details.estimated_value = source_details.estimated_value
            target_details.statute_of_limitations = source_details.statute_of_limitations
            target_details.street1 = source_details.street1
            target_details.street2 = source_details.street2
            target_details.city = source_details.city
            target_details.county = source_details.county
            target_details.state = source_details.state
            target_details.zip_code = source_details.zip_code
            target_details.requested_at = source_details.requested_at
            target_details.requested_by = source_details.requested_by
            target_details.received_at = source_details.received_at
            target_details.received_by = source_details.received_by
            target_details.save()

        # Create sync relationship
        self.get_or_create_sync_relationship(source_details, target_details, content_type)


class CasePartySyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync witnesses and other parties between cases"""

    def get_serializer_class(self):
        return CasePartySerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync witnesses and other parties from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)
        party_id = request.data.get("party_id", [])

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_parties(source_case, target_case, party_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}
                    errors.append({"case_id": target_case.id, "error": str(error_details)})

        return Response({"message": "Parties synced successfully", "errors": errors, "synced_cases": synced_objects})

    def _sync_parties(self, source_case, target_case, party_id):
        """Sync all parties between cases"""
        source_party = CaseParty.objects.get(id=party_id)
        source_contact = source_party.contact

        # Handle party sync with its one-to-one contact relationship
        content_type_party = ContentType.objects.get_for_model(CaseParty)
        synced_parties_dict = self.get_all_synced_objects(source_party, content_type_party)

        target_party = None
        for obj_id, obj in synced_parties_dict.items():
            if obj.target_object_id in CaseParty.objects.filter(case=target_case).values_list("id", flat=True):
                target_party = CaseParty.objects.get(id=obj.target_object_id)
                break

        if not target_party:
            # Create new party with its contact
            target_contact = None
            if source_contact:
                target_contact = CasePartyContact.objects.create(
                    case=target_case,
                    party_type=source_contact.party_type,
                    payee=source_contact.payee,
                    first_name=source_contact.first_name,
                    last_name=source_contact.last_name,
                    phone=source_contact.phone,
                    phone_ext=source_contact.phone_ext,
                    cell=source_contact.cell,
                    fax=source_contact.fax,
                    email=source_contact.email,
                    website=source_contact.website,
                    street1=source_contact.street1,
                    street2=source_contact.street2,
                    city=source_contact.city,
                    state=source_contact.state,
                    zip_code=source_contact.zip_code,
                    tax_id=source_contact.tax_id,
                    note=source_contact.note,
                )

            target_party = CaseParty.objects.create(
                case=target_case,
                contact=target_contact,
                status=source_party.status,
                description=source_party.description,
                law_firm=source_party.law_firm,
                attorney=source_party.attorney,
            )
        else:
            # Update existing party and its contact
            if source_contact:
                if not target_party.contact:
                    # Create new contact for existing party
                    target_contact = CasePartyContact.objects.create(
                        case=target_case,
                        party_type=source_contact.party_type,
                        payee=source_contact.payee,
                        first_name=source_contact.first_name,
                        last_name=source_contact.last_name,
                        phone=source_contact.phone,
                        phone_ext=source_contact.phone_ext,
                        cell=source_contact.cell,
                        fax=source_contact.fax,
                        email=source_contact.email,
                        website=source_contact.website,
                        street1=source_contact.street1,
                        street2=source_contact.street2,
                        city=source_contact.city,
                        state=source_contact.state,
                        zip_code=source_contact.zip_code,
                        tax_id=source_contact.tax_id,
                        note=source_contact.note,
                    )
                    target_party.contact = target_contact
                else:
                    # Update existing contact
                    target_contact = target_party.contact
                    target_contact.party_type = source_contact.party_type
                    target_contact.payee = source_contact.payee
                    target_contact.first_name = source_contact.first_name
                    target_contact.last_name = source_contact.last_name
                    target_contact.phone = source_contact.phone
                    target_contact.phone_ext = source_contact.phone_ext
                    target_contact.cell = source_contact.cell
                    target_contact.fax = source_contact.fax
                    target_contact.email = source_contact.email
                    target_contact.website = source_contact.website
                    target_contact.street1 = source_contact.street1
                    target_contact.street2 = source_contact.street2
                    target_contact.city = source_contact.city
                    target_contact.state = source_contact.state
                    target_contact.zip_code = source_contact.zip_code
                    target_contact.tax_id = source_contact.tax_id
                    target_contact.note = source_contact.note
                    target_contact.save()

            # Update party fields
            target_party.status = source_party.status
            target_party.description = source_party.description
            target_party.law_firm = source_party.law_firm
            target_party.attorney = source_party.attorney
            target_party.save()

        # Create sync relationship only for the party
        self.get_or_create_sync_relationship(source_party, target_party, ContentType.objects.get_for_model(CaseParty))


class TreatmentProviderSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync medical treatment providers between cases"""

    def get_serializer_class(self):
        return TreatmentProviderSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync medical treatment providers from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        treatment_provider_id = request.data.get("treatment_provider_id", [])

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        target_cases = Case.objects.filter(id__in=target_cases)

        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_treatment_providers(source_case, target_case, treatment_provider_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}

                    errors.append({"case_id": target_case.id, "error": str(error_details)})

        return Response(
            {
                "message": "Treatment providers synced successfully",
                "errors": errors if errors else None,
                "sycned_cases": synced_objects,
            }
        )

    def _sync_treatment_providers(self, source_case, target_case, treatment_provider_id):
        """Sync treatment providers between cases"""
        source_provider = TreatmentProvider.objects.get(id=treatment_provider_id)

        content_type = ContentType.objects.get_for_model(TreatmentProvider)
        synced_objects_dict = self.get_all_synced_objects(source_provider, content_type)

        target_provider = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id == TreatmentProvider.objects.filter(case=target_case).values_list("id", flat=True):
                target_provider = TreatmentProvider.objects.get(id=obj.target_object_id)
                print(f"Found existing synced treatment provider {target_provider}")
                break

        if not target_provider:
            # Get or create treatment provider
            target_provider = TreatmentProvider.objects.create(
                case=target_case,
                medical_provider=source_provider.medical_provider,
            )

            # Create sync relationship
            self.get_or_create_sync_relationship(
                source_provider, target_provider, ContentType.objects.get_for_model(TreatmentProvider)
            )
        else:
            target_provider.medical_provider = source_provider.medical_provider
            target_provider.save()


class CaseAttorneyLienSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync attorney liens between cases"""

    def get_serializer_class(self):
        return CaseAttorneyLienSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync attorney liens from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)
        attorney_lien_id = request.data.get("attorney_lien_id")

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_attorney_liens(source_case, target_case, attorney_lien_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}

                    errors.append({"case_id": target_case.id, "error": error_details})

        return Response(
            {"message": "Attorney liens synced successfully", "errors": errors, "synced_cases": synced_objects}
        )

    def _sync_attorney_liens(self, source_case, target_case, attorney_lien_id):
        """Sync attorney liens between cases"""
        source_lien = CaseAttorneyLien.objects.get(id=attorney_lien_id)

        content_type = ContentType.objects.get_for_model(CaseAttorneyLien)
        synced_objects_dict = self.get_all_synced_objects(source_lien, content_type)

        target_lien = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id in CaseAttorneyLien.objects.filter(case=target_case).values_list("id", flat=True):
                target_lien = CaseAttorneyLien.objects.get(id=obj.target_object_id)
                break

        if not target_lien:
            # Get or create attorney lien with only law firm and attorney
            target_lien = CaseAttorneyLien.objects.create(
                case=target_case, law_firm=source_lien.law_firm, attorney=source_lien.attorney, fee_amount=Decimal(0)
            )
        else:
            target_lien.law_firm = source_lien.law_firm
            target_lien.attorney = source_lien.attorney
            target_lien.fee_amount = Decimal(0)
            target_lien.save()

        # Create sync relationship
        self.get_or_create_sync_relationship(
            source_lien, target_lien, ContentType.objects.get_for_model(CaseAttorneyLien)
        )


class CaseMiscellaneousLienSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync miscellaneous liens between cases"""

    def get_serializer_class(self):
        return CaseMiscellaneousLienSerializer

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync attorney liens from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)
        miscellaneous_lien_id = request.data.get("miscellaneous_lien_id")

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_miscellaneous_liens(source_case, target_case, miscellaneous_lien_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}

                    errors.append({"case_id": target_case.id, "error": error_details})

        return Response(
            {"message": "Miscellaneous liens synced successfully", "errors": errors, "synced_cases": synced_objects}
        )

    def _sync_miscellaneous_liens(self, source_case, target_case, miscellaneous_lien_id):
        """Sync miscellaneous liens between cases"""
        source_lien = CaseMiscellaneousLien.objects.get(id=miscellaneous_lien_id)

        content_type = ContentType.objects.get_for_model(CaseMiscellaneousLien)
        synced_objects_dict = self.get_all_synced_objects(source_lien, content_type)

        target_lien = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id in CaseMiscellaneousLien.objects.filter(case=target_case).values_list(
                "id", flat=True
            ):
                target_lien = CaseMiscellaneousLien.objects.get(id=obj.target_object_id)
                break

        if not target_lien:
            # Get or create attorney lien with only law firm and attorney
            target_lien = CaseMiscellaneousLien.objects.create(
                case=target_case,
                lien_holder=source_lien.lien_holder,
                lien_name=source_lien.lien_name,
                lien_amount=Decimal(0),
            )
        else:
            target_lien.lien_holder = source_lien.lien_holder
            target_lien.lien_name = source_lien.lien_name
            target_lien.lien_amount = Decimal(0)
            target_lien.save()

        # Create sync relationship
        self.get_or_create_sync_relationship(
            source_lien, target_lien, ContentType.objects.get_for_model(CaseAttorneyLien)
        )


class CaseWorkersSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync case workers between cases"""

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync case workers from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_workers(source_case, target_case)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    errors.append({"case_id": target_case.id, "error": str(e)})

        return Response(
            {"message": "Case workers synced successfully", "errors": errors, "synced_cases": synced_objects}
        )

    def _sync_workers(self, source_case, target_case):
        """Sync workers between cases"""
        source_workers = source_case.v2_workers
        if not source_workers:
            return

        # Get or create target workers
        target_workers, _ = CaseWorkers.objects.get_or_create(case=target_case)

        # Sync all worker fields
        worker_fields = [
            "primary_contact",
            "case_manager",
            "lead_attorney",
            "case_assistant",
            "lien_negotiator",
            "supervising_attorney",
            "intake_specialist",
            "investigator",
            "accountant",
            "litigation_firm",
            "litigation_attorney",
            "litigation_assistant",
            "accident_recreation_worker",
        ]

        for field in worker_fields:
            setattr(target_workers, field, getattr(source_workers, field))

        target_workers.save()

        # Create sync relationship
        self.get_or_create_sync_relationship(
            source_workers, target_workers, ContentType.objects.get_for_model(CaseWorkers)
        )


class CaseStatusSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync case status between cases"""

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """Sync case status from source case to target cases"""
        # Get source case
        try:
            source_case = Case.objects.get(id=case_id)
        except Case.DoesNotExist:
            return Response({"error": "Source case not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get target case IDs from request
        target_case_ids = request.data.get("target_case_ids", [])
        if not target_case_ids:
            return Response({"error": "No target cases provided"}, status=status.HTTP_400_BAD_REQUEST)

        # Get target cases
        try:
            target_cases = Case.objects.filter(id__in=target_case_ids)
            if len(target_cases) != len(target_case_ids):
                return Response({"error": "One or more target cases not found"}, status=status.HTTP_404_NOT_FOUND)
        except Case.DoesNotExist:
            return Response({"error": "Target cases not found"}, status=status.HTTP_404_NOT_FOUND)

        # Update status for each target case
        for target_case in target_cases:
            target_case.status = source_case.status
            target_case.organization_status = source_case.organization_status
            target_case.save()

        return Response(
            {
                "message": f"Successfully synced status from case {source_case.id} to {len(target_cases)} target cases",
                "source_case_id": source_case.id,
                "target_case_ids": target_case_ids,
            },
            status=status.HTTP_200_OK,
        )


class CaseExpertWitnessSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync expert witnesses between cases"""

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync expert witnesses from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)
        expert_witness_id = request.data.get("expert_witness_id", [])

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_expert_witness(source_case, target_case, expert_witness_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}
                    errors.append({"case_id": target_case.id, "error": str(error_details)})

        return Response(
            {
                "message": "Expert witnesses synced successfully",
                "errors": errors if errors else None,
                "synced_cases": synced_objects,
            }
        )

    def _sync_expert_witness(self, source_case, target_case, expert_witness_id):
        """Sync expert witness between cases"""
        source_expert = CaseExpertWitness.objects.get(id=expert_witness_id)

        # Handle expert witness sync
        content_type = ContentType.objects.get_for_model(CaseExpertWitness)
        synced_objects_dict = self.get_all_synced_objects(source_expert, content_type)

        target_expert = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id in CaseExpertWitness.objects.filter(case=target_case).values_list("id", flat=True):
                target_expert = CaseExpertWitness.objects.get(id=obj.target_object_id)
                break

        if not target_expert:
            # Create new expert witness assignment
            target_expert = CaseExpertWitness.objects.create(
                case=target_case,
                expert_witness=source_expert.expert_witness,  # Use the same organization-level contact
                type=source_expert.type,
                description=source_expert.description,
                retained_date=source_expert.retained_date,
                record_received_date=source_expert.record_received_date,
            )
        else:
            # Update existing expert witness assignment
            target_expert.type = source_expert.type
            target_expert.description = source_expert.description
            target_expert.retained_date = source_expert.retained_date
            target_expert.record_received_date = source_expert.record_received_date
            target_expert.save()

        # Create sync relationship for the expert witness assignment
        self.get_or_create_sync_relationship(source_expert, target_expert, content_type)


class CaseHealthInsuranceSyncViewSet(CaseSyncMixin, viewsets.GenericViewSet):
    """Sync health insurance between cases"""

    def get_serializer_class(self):
        return None

    @action(detail=False, methods=["post"])
    def sync(self, request, case_id=None):
        """
        Sync health insurance from source case to target cases
        """
        source_case = case = Case.objects.get(id=case_id)
        target_cases = request.data.get("target_case_ids", [])
        target_cases = Case.objects.filter(id__in=target_cases)
        health_insurance_id = request.data.get("health_insurance_id", [])

        if not target_cases:
            return Response({"error": "No target cases specified"}, status=400)

        synced_objects = []
        errors = []
        with transaction.atomic():
            for target_case in target_cases:
                try:
                    self._sync_health_insurance(source_case, target_case, health_insurance_id)
                    synced_objects.append(target_case.id)
                except Exception as e:
                    import traceback

                    error_details = {"error_message": str(e), "traceback": traceback.format_exc()}
                    errors.append({"case_id": target_case.id, "error": str(error_details)})

        return Response(
            {
                "message": "Health insurance synced successfully",
                "errors": errors if errors else None,
                "synced_cases": synced_objects,
            }
        )

    def _sync_health_insurance(self, source_case, target_case, health_insurance_id):
        """Sync health insurance between cases"""
        source_health_insurance = HealthInsurance.objects.get(id=health_insurance_id)

        # Handle health insurance sync
        content_type = ContentType.objects.get_for_model(HealthInsurance)
        synced_objects_dict = self.get_all_synced_objects(source_health_insurance, content_type)

        target_health_insurance = None
        for obj_id, obj in synced_objects_dict.items():
            if obj.target_object_id in HealthInsurance.objects.filter(case=target_case).values_list("id", flat=True):
                target_health_insurance = HealthInsurance.objects.get(id=obj.target_object_id)
                break

        if not target_health_insurance:
            # Create new health insurance
            try:
                target_health_insurance = HealthInsurance.objects.create(
                    case=target_case,
                    insurance_company=source_health_insurance.insurance_company,
                    subrogation_company=source_health_insurance.subrogation_company,
                )
            except Exception as e:
                info_logger.info(f"Error happend while trying to create {str(e)}")
                pass

        else:
            # Update existing health insurance
            target_health_insurance.insurance_company = source_health_insurance.insurance_company
            target_health_insurance.subrogation_company = source_health_insurance.subrogation_company
            target_health_insurance.save()

        # Create sync relationship for the health insurance
        _, synced_objects = self.get_or_create_sync_relationship(
            source_health_insurance, target_health_insurance, content_type
        )
        info_logger.info(f"objects craeted {synced_objects}")
