from datetime import datetime
from decimal import Decimal
from urllib.parse import unquote
from zoneinfo import ZoneInfo

from django.db import models
from django.db.models import Q
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from kpi_and_reports.models import KPIType
from rest_framework import generics, serializers, status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.filters import BaseFilterBackend, OrderingFilter, SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from users.models import NylasIntegration, User
from users.nylas_integration.config import nylas_client
from users.serializers import UserSerializer
from utils.utils import LoggerMixin, error_logger

from case_management.models import Case, Case<PERSON>he<PERSON><PERSON>, CaseLink, CaseStatus, Task
from case_management.serializers import <PERSON>Serializer
from case_management.v2.utils.conflict_checker import (
    get_case_conflicts,
    get_unresolved_conflicts,
)

from .mixins import SyncHelperMixin
from .models import (
    AdjusterContact,
    CaseAttorneyLien,
    CaseCost,
    CaseCourtDetails,
    CaseDefendant,
    CaseDefendantLegalRepresentation,
    CaseDefendantLitigationServiceDates,
    CaseDiscovery,
    CaseEvent,
    CaseExpertWitness,
    CaseImpactAssessment,
    CaseIncidentDetails,
    CaseJudgeDetails,
    CaseMediatorDetails,
    CaseMiscellaneousLien,
    CaseNegotiation,
    CaseNegotiationUIM,
    CaseParty,
    CasePartyContact,
    CaseSettlementCalculation,
    CaseWorkers,
    ClerkContact,
    ClientBasicDetails,
    ClientContactDetails,
    ClientInsurance,
    ClientPropertyDamage,
    ClientTrust,
    ClientTrustEntryType,
    Conflict,
    CourtContact,
    DefendantInsurance,
    DefendantInsuranceAdjuster,
    DefendantPropertyDamage,
    DiscoverExtension,
    DiscoveryResponse,
    Employer,
    EmployerWorkersCompensation,
    ExpertWitnessContact,
    HealthInsurance,
    InsuranceAdjuster,
    InsuranceCompany,
    InsuranceLegalRepresentation,
    JudgeContact,
    LawFirmContact,
    LienHolder,
    LoanCompanyContact,
    ManualSettlementEntry,
    MediatorContact,
    MedicalProvider,
    MedicalProviderContact,
    MedPayDeposit,
    MedPayDepositStatus,
    OrganizationAttorneyContact,
    OrgCostContact,
    SettlementAdvance,
    SubrogationCompany,
    SubrogationContact,
    TreatmentProvider,
)
from .permissions import BaseV2Permission, NestedOrganizationPermission, OrganizationLevelPermission
from .serializers import (
    AdjusterContactSerializer,
    CaseAttorneyLienReadSerializer,
    CaseAttorneyLienSerializer,
    CaseCostSerializer,
    CaseCourtDetailsReadSerializer,
    CaseCourtDetailsWriteSerializer,
    CaseCreateV2Serializer,
    CaseDefendantLegalRepresentationReadSerializer,
    CaseDefendantLegalRepresentationWriteSerializer,
    CaseDefendantLitigationServiceDatesSerializer,
    CaseDefendantReadSerializer,
    CaseDefendantSerializer,
    CaseDiscoveryReadSerializer,
    CaseDiscoveryWriteSerializer,
    CaseEventReadSerializer,
    CaseEventWriteSerializer,
    CaseExpertWitnessReadSerializer,
    CaseExpertWitnessWriteSerializer,
    CaseImpactAssessmentSerializer,
    CaseIncidentDetailsReadSerializer,
    CaseIncidentDetailsSerializer,
    CaseJudgeDetailsReadSerializer,
    CaseJudgeDetailsWriteSerializer,
    CaseLinkCreateSerializer,
    CaseLinkSerializer,
    CaseListSerializer,
    CaseMediatorDetailsReadSerializer,
    CaseMediatorDetailsWriteSerializer,
    CaseMiscellaneousLienReadSerializer,
    CaseMiscellaneousLienSerializer,
    CaseNegotiationReadSerializer,
    CaseNegotiationUIMReadSerializer,
    CaseNegotiationUIMWriteSerializer,
    CaseNegotiationWriteSerializer,
    CasePartyContactSerializer,
    CasePartyReadSerializer,
    CasePartySerializer,
    CaseSettlementCalculationReadSerializer,
    CaseSettlementCalculationUpdateFeesSerializer,
    CaseWorkersReadSerializer,
    CaseWorkersWriteSerializer,
    ClerkContactSerializer,
    ClientBasicDetailsSerializer,
    ClientContactDetailsSerializer,
    ClientInsuranceReadSerializer,
    ClientInsuranceSerializer,
    ClientPropertyDamageReadSerializer,
    ClientPropertyDamageWriteSerializer,
    ClientTrustSerializer,
    ConflictResolutionSerializer,
    ConflictSerializer,
    CourtContactSerializer,
    DefendantAdjusterListSerializer,
    DefendantInsuranceAdjusterReadSerializer,
    DefendantInsuranceAdjusterWriteSerializer,
    DefendantInsuranceReadSerializer,
    DefendantInsuranceSerializer,
    DefendantPropertyDamageReadSerializer,
    DefendantPropertyDamageWriteSerializer,
    DiscoveryExtensionSerializer,
    DiscoveryResponseSerializer,
    EmployerReadSerializer,
    EmployerSerializer,
    EmployerWorkersCompensationSerializer,
    ExpertWitnessContactSerializer,
    HealthInsuranceReadSerializer,
    HealthInsuranceSerializer,
    InsuranceAdjusterReadSerializer,
    InsuranceAdjusterSerializer,
    InsuranceCompanySerializer,
    InsuranceLegalRepresentationReadSerializer,
    InsuranceLegalRepresentationSerializer,
    JudgeContactSerializer,
    LawFirmContactSerializer,
    LienHolderSerializer,
    LoanCompanyContactSerializer,
    ManualSettlementEntrySerializer,
    MediatorContactSerializer,
    MedicalProviderContactSerializer,
    MedicalProviderSerializer,
    MedPayDepositReadSerializer,
    MedPayDepositSerializer,
    NewLinkedCaseCreateSerializer,
    OrganizationAttorneyContactSerializer,
    OrgCostContactSerializer,
    SettlementAdvanceReadSerializer,
    SettlementAdvanceWriteSerializer,
    SubrogationCompanySerializer,
    SubrogationContactSerializer,
    TreatmentProviderReadSerializer,
    TreatmentProviderSerializer,
    UserDetailSerializer,
)

# Organization-Level Views


class OrganizationUserListView(generics.ListAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = UserSerializer
    pagination_class = None  # Disable pagination for this view

    def get_queryset(self):
        role = self.request.query_params.get("role")
        # Get the organization from the case
        organization = self.request.user.organizations.first()
        if not organization:
            return []

        queryset = organization.users.all().order_by("id")  # Add ordering for consistent results
        if role:
            queryset = queryset.filter(role=role)
        return queryset


# Insurance Company Views


class InsuranceCompanyListCreateView(generics.ListCreateAPIView):
    serializer_class = InsuranceCompanySerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return InsuranceCompany.objects.none()
        return InsuranceCompany.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class InsuranceCompanyDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = InsuranceCompanySerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return InsuranceCompany.objects.none()
        return InsuranceCompany.objects.filter(organization=organization)


# Adjuster Views


class AdjusterContactListCreateView(generics.ListCreateAPIView):
    serializer_class = AdjusterContactSerializer
    permission_classes = [IsAuthenticated, NestedOrganizationPermission]
    pagination_class = None

    def get_queryset(self):
        insurance_company_id = self.kwargs.get("insurance_company_id")
        insurance_company = get_object_or_404(
            InsuranceCompany, id=insurance_company_id, organization=self.request.user.organizations.first()
        )
        return AdjusterContact.objects.filter(insurance_company=insurance_company)

    def perform_create(self, serializer):
        insurance_company = get_object_or_404(
            InsuranceCompany,
            id=self.kwargs.get("insurance_company_id"),
            organization=self.request.user.organizations.first(),
        )
        serializer.save(insurance_company=insurance_company)


class AdjusterContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = AdjusterContactSerializer
    permission_classes = [IsAuthenticated, NestedOrganizationPermission]
    pagination_class = None

    def get_queryset(self):
        insurance_company_id = self.kwargs.get("insurance_company_id")
        insurance_company = get_object_or_404(
            InsuranceCompany, id=insurance_company_id, organization=self.request.user.organizations.first()
        )
        return AdjusterContact.objects.filter(insurance_company=insurance_company)


# Law Firm Views


class LawFirmContactListCreateView(generics.ListCreateAPIView):
    serializer_class = LawFirmContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return LawFirmContact.objects.none()
        return LawFirmContact.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class LawFirmContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = LawFirmContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return LawFirmContact.objects.none()
        return LawFirmContact.objects.filter(organization=organization)


# Case-Level Views


class ClientBasicDetailsView(generics.RetrieveUpdateAPIView):
    serializer_class = ClientBasicDetailsSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_object(self):
        case_id = self.kwargs["case_id"]
        return get_object_or_404(ClientBasicDetails, case_id=case_id)

    def get(self, request, *args, **kwargs):
        """Get client basic details for a case"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Http404:
            # Create empty record if case exists
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in request.user.organizations.all():
                raise PermissionDenied

            # Create empty record with only required fields
            instance = ClientBasicDetails.objects.create(
                case=case,
                first_name="",  # Required field but allow empty string
                last_name="",  # Required field but allow empty string
                gender="Male",  # Required field, set default
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)


class ClientContactDetailsView(generics.RetrieveUpdateAPIView):
    serializer_class = ClientContactDetailsSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_object(self):
        case_id = self.kwargs["case_id"]
        return get_object_or_404(ClientContactDetails, case_id=case_id)

    def get(self, request, *args, **kwargs):
        """Get client contact details for a case"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Http404:
            # Create empty record if case exists
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in request.user.organizations.all():
                raise PermissionDenied

            # Create empty record
            instance = ClientContactDetails.objects.create(case=case)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)


class CaseWorkersView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseWorkersReadSerializer
        return CaseWorkersWriteSerializer

    def get_object(self):
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        return CaseWorkers.objects.filter(case=case).first()

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            # Create empty record if case exists
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in request.user.organizations.all():
                raise PermissionDenied

            # Create empty record
            instance = CaseWorkers.objects.create(case=case)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance:
            serializer = self.get_serializer(instance, data=request.data, partial=True)
        else:
            serializer = self.get_serializer(data=request.data)

        serializer.is_valid(raise_exception=True)
        serializer.save(case_id=self.kwargs["case_id"])
        return Response(serializer.data, status=status.HTTP_200_OK)


class CaseWorkersAvailableUsersView(APIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get(self, request, case_id):
        """Get available users for case worker roles."""
        role = request.query_params.get("role")
        # Get the organization from the case
        case = get_object_or_404(Case, id=case_id)
        organization = case.organization
        if not organization:
            return []

        queryset = organization.users.all()
        if role:
            queryset = queryset.filter(role=role)

        serializer = UserDetailSerializer(queryset, many=True)
        return Response(serializer.data)


# Insurance-Level Views


class ClientInsuranceListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["no_insurance", "claim_number", "policy_number", "coverage_status", "liability_status"]
    search_fields = ["claim_number", "policy_number", "insured_name"]
    ordering_fields = ["created_at", "updated_at"]
    ordering = ["created_at"]
    pagination_class = None  # Disable pagination

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ClientInsuranceReadSerializer
        return ClientInsuranceSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return ClientInsurance.objects.filter(case=case).select_related(
            "insurance_company", "case", "case__organization"
        )

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        serializer.save(case=case)


class ClientInsuranceDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ClientInsuranceReadSerializer
        return ClientInsuranceSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return ClientInsurance.objects.filter(case=case).select_related(
            "insurance_company", "case", "case__organization"
        )

    def perform_update(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied

        # Handle no_insurance case
        if serializer.validated_data.get("no_insurance", False):
            # If no_insurance is True, set insurance_company to None and clear other fields
            serializer.save(case=case, insurance_company=None, policy_number=None, claim_number=None, policy_type=None)
        else:
            # For regular insurance entries, require insurance_company
            if "insurance_company" not in serializer.validated_data:
                raise serializers.ValidationError(
                    {"insurance_company": "Insurance company is required when no_insurance is False"}
                )
            serializer.save(case=case)


class InsuranceAdjusterView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return InsuranceAdjusterReadSerializer
        return InsuranceAdjusterSerializer

    def get_object(self):
        try:
            # First check if the case and insurance belong to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"], case=case)
            adjuster = InsuranceAdjuster.objects.filter(client_insurance=client_insurance).first()
            if adjuster:
                return adjuster
            # For PUT/POST methods, return None to indicate creation is needed
            if self.request.method != "GET":
                return None
            # For GET, we'll handle the creation in the get method
            raise Http404
        except Case.DoesNotExist:
            raise Http404

    def get(self, request, *args, **kwargs):
        """Get adjuster assignment for client insurance"""
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"], case=case)
            adjuster = InsuranceAdjuster.objects.filter(client_insurance=client_insurance).first()

            if not adjuster:
                # Create a new empty record
                adjuster = InsuranceAdjuster.objects.create(
                    client_insurance=client_insurance,
                    bodily_injury=None,
                    bi_supervisor=None,
                    medpay_pip=None,
                    medpay_pip_supervisor=None,
                    property_damage=None,
                    pd_supervisor=None,
                )

            serializer = self.get_serializer(adjuster)
            return Response(serializer.data)
        except Case.DoesNotExist:
            raise Http404

    def post(self, request, *args, **kwargs):
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"], case=case)

            # Check if adjuster already exists
            if InsuranceAdjuster.objects.filter(client_insurance=client_insurance).exists():
                return Response({"detail": "Insurance adjuster already exists"}, status=status.HTTP_400_BAD_REQUEST)

            # Add client_insurance to request data
            data = request.data.copy()
            data["client_insurance"] = client_insurance.id

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except (Case.DoesNotExist, ClientInsurance.DoesNotExist):
            raise Http404

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            # Create a new adjuster if it doesn't exist
            try:
                case = get_object_or_404(Case, id=self.kwargs["case_id"])
                client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"], case=case)

                # Create a new empty record
                instance = InsuranceAdjuster.objects.create(
                    client_insurance=client_insurance,
                    bodily_injury=None,
                    bi_supervisor=None,
                    medpay_pip=None,
                    medpay_pip_supervisor=None,
                    property_damage=None,
                    pd_supervisor=None,
                )
            except (Case.DoesNotExist, ClientInsurance.DoesNotExist):
                return Response({"detail": "Insurance adjuster not found"}, status=status.HTTP_404_NOT_FOUND)

        # Add client_insurance to request data
        data = request.data.copy()
        data["client_insurance"] = self.kwargs["insurance_id"]

        serializer = self.get_serializer(instance=instance, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class InsuranceLegalRepresentationView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["attorney", "law_firm", "co_counsel_attorney", "co_counsel_law_firm"]
    search_fields = ["attorney__name", "law_firm__name", "co_counsel_attorney__name", "co_counsel_law_firm__name"]
    ordering_fields = ["created_at", "updated_at"]
    ordering = ["created_at"]
    pagination_class = None  # Disable pagination

    def get_serializer_class(self):
        if self.request.method == "GET":
            return InsuranceLegalRepresentationReadSerializer
        return InsuranceLegalRepresentationSerializer

    def get_queryset(self):
        client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"])
        if client_insurance.case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return InsuranceLegalRepresentation.objects.filter(client_insurance=client_insurance).select_related(
            "attorney",
            "law_firm",
            "co_counsel_attorney",
            "co_counsel_law_firm",
            "client_insurance",
            "client_insurance__case",
            "client_insurance__case__organization",
        )

    def perform_create(self, serializer):
        client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"])
        if client_insurance.case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        serializer.save(client_insurance=client_insurance)


class InsuranceLegalRepresentationDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return InsuranceLegalRepresentationReadSerializer
        return InsuranceLegalRepresentationSerializer

    def get_queryset(self):
        client_insurance = get_object_or_404(ClientInsurance, id=self.kwargs["insurance_id"])
        if client_insurance.case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return InsuranceLegalRepresentation.objects.filter(client_insurance=client_insurance).select_related(
            "attorney",
            "law_firm",
            "co_counsel_attorney",
            "co_counsel_law_firm",
            "client_insurance",
            "client_insurance__case",
            "client_insurance__case__organization",
        )

    def get_object(self):
        queryset = self.get_queryset()
        obj = get_object_or_404(queryset, pk=self.kwargs["pk"])
        self.check_object_permissions(self.request, obj)
        return obj

    def update(self, request, *args, **kwargs):
        # Use write serializer for validation and update
        write_serializer = InsuranceLegalRepresentationSerializer(
            data=request.data, instance=self.get_object(), partial=kwargs.get("partial", False)
        )
        write_serializer.is_valid(raise_exception=True)
        instance = write_serializer.save()

        # Use read serializer for response
        read_serializer = InsuranceLegalRepresentationReadSerializer(instance)
        return Response(read_serializer.data)


# Organization Attorney Views


class OrganizationAttorneyListCreateView(generics.ListCreateAPIView):
    serializer_class = OrganizationAttorneyContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return OrganizationAttorneyContact.objects.none()
        return OrganizationAttorneyContact.objects.filter(organization=organization)


class OrganizationAttorneyDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = OrganizationAttorneyContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return OrganizationAttorneyContact.objects.none()
        return OrganizationAttorneyContact.objects.filter(organization=organization)


# Case Defendant Views


class CaseDefendantListCreateView(SyncHelperMixin, generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["defendant_type"]
    search_fields = ["first_name", "last_name", "email"]
    ordering_fields = ["created_at", "first_name", "last_name"]
    ordering = ["created_at"]
    pagination_class = None  # Disable pagination for this view

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseDefendantReadSerializer
        return CaseDefendantSerializer

    def get_queryset(self):
        # Get the case and verify organization access
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied

        # Filter defendants by case and prefetch related data
        return (
            CaseDefendant.objects.filter(case=case)
            .select_related("case")
            .prefetch_related(
                "insurances",
                "insurances__insurance_company",
                "legal_representation",
                "legal_representation__law_firm",
                "legal_representation__attorney",
            )
            .order_by("created_at")
        )

    def perform_create(self, serializer):
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404("Case not found")  # Return 404 instead of PermissionDenied
            defendant = serializer.save(case=case)
        except Case.DoesNotExist:
            raise Http404("Case not found")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["sync_helper"] = self
        return context


class CaseDefendantDetailView(SyncHelperMixin, generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        return (
            CaseDefendant.objects.filter(case=case)
            .select_related("case")
            .prefetch_related(
                "insurances",
                "insurances__insurance_company",
                "legal_representation",
                "legal_representation__law_firm",
                "legal_representation__attorney",
            )
        )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseDefendantReadSerializer
        return CaseDefendantSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["sync_helper"] = self
        return context

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)


class DefendantPropertyDamageView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return DefendantPropertyDamageReadSerializer
        return DefendantPropertyDamageWriteSerializer

    def get_object(self):
        try:
            # First check if the case belongs to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            # Then get the defendant and its property damage
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            return get_object_or_404(DefendantPropertyDamage, defendant=defendant)
        except Case.DoesNotExist:
            raise Http404

    def get(self, request, *args, **kwargs):
        """Get defendant property damage"""
        try:
            # First check if the case belongs to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            # Then get the defendant
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)

            # Get or return None if not found
            property_damage = DefendantPropertyDamage.objects.filter(defendant=defendant).first()
            if not property_damage:
                return Response(None)

            serializer = self.get_serializer(property_damage)
            return Response(serializer.data)
        except Case.DoesNotExist:
            raise Http404

    def post(self, request, *args, **kwargs):
        """Create defendant property damage"""
        try:
            # First check if the case belongs to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            # Then get the defendant
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)

            # Check if property damage already exists
            if DefendantPropertyDamage.objects.filter(defendant=defendant).exists():
                return Response(
                    {"detail": "Property damage already exists for this defendant"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            serializer = self.get_serializer(data=request.data, context={"defendant_id": defendant.id})
            serializer.is_valid(raise_exception=True)
            serializer.save(defendant=defendant)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Case.DoesNotExist:
            raise Http404

    def put(self, request, *args, **kwargs):
        """Update defendant property damage"""
        try:
            # First check if the case belongs to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            # Then get the defendant
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)

            # Get property damage or 404
            property_damage = get_object_or_404(DefendantPropertyDamage, defendant=defendant)

            serializer = self.get_serializer(property_damage, data=request.data, context={"defendant_id": defendant.id})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except Case.DoesNotExist:
            raise Http404

    def delete(self, request, *args, **kwargs):
        """Delete defendant property damage"""
        try:
            # First check if the case belongs to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            # Then get the defendant
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)

            # Get property damage or 404
            property_damage = get_object_or_404(DefendantPropertyDamage, defendant=defendant)
            property_damage.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Case.DoesNotExist:
            raise Http404


class CaseDefendantLitigationServiceDatesView(generics.RetrieveUpdateAPIView):
    """View for managing litigation service dates for a case defendant"""

    serializer_class = CaseDefendantLitigationServiceDatesSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_object(self):
        return get_object_or_404(
            CaseDefendantLitigationServiceDates,
            defendant_id=self.kwargs["defendant_id"],
            defendant__case_id=self.kwargs["case_id"],
        )

    def get(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Http404:
            # Create empty record if defendant exists
            defendant = get_object_or_404(
                CaseDefendant,
                id=self.kwargs["defendant_id"],
                case_id=self.kwargs["case_id"],
            )
            instance = CaseDefendantLitigationServiceDates.objects.create(defendant=defendant)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        defendant = get_object_or_404(
            CaseDefendant,
            id=self.kwargs["defendant_id"],
            case_id=self.kwargs["case_id"],
        )
        # Check if litigation dates already exist
        if CaseDefendantLitigationServiceDates.objects.filter(defendant=defendant).exists():
            return Response(
                {"detail": "Litigation service dates already exist for this defendant"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(defendant=defendant)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class PartyTypeFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        party_type = request.query_params.get("party_type")
        if party_type:
            if isinstance(queryset.first(), CaseParty):
                return queryset.filter(contact__party_type=party_type)
            else:
                return queryset.filter(party_type=party_type)
        return queryset


class CasePartyListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None
    filter_backends = [PartyTypeFilterBackend]

    def get_queryset(self):
        return CaseParty.objects.filter(case_id=self.kwargs["case_id"]).select_related(
            "contact", "law_firm", "attorney"
        )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CasePartyReadSerializer
        return CasePartySerializer

    def perform_create(self, serializer):
        # Get the case from URL parameter and check organization access
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise PermissionDenied("You do not have permission to create parties for this case.")
        # Save with the case
        serializer.save(case=case)


class CasePartyDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = CasePartySerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_queryset(self):
        return CaseParty.objects.filter(case_id=self.kwargs["case_id"])


class CasePartyContactView(generics.ListCreateAPIView):
    serializer_class = CasePartyContactSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None
    filter_backends = [PartyTypeFilterBackend]

    def get_queryset(self):
        return CasePartyContact.objects.filter(case_id=self.kwargs["case_id"])

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise PermissionDenied("You do not have permission to create contacts for this case.")
        serializer.save(case=case)


class CasePartyContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = CasePartyContactSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    lookup_url_kwarg = "contact_id"
    lookup_field = "id"

    def get_queryset(self):
        return CasePartyContact.objects.filter(case_id=self.kwargs["case_id"])


class DefendantInsuranceListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["coverage_status", "liability_status"]
    search_fields = ["policy_number", "claim_number"]
    ordering_fields = ["created_at", "updated_at"]
    pagination_class = None

    def get_queryset(self):
        case_id = self.kwargs.get("case_id")
        defendant_id = self.kwargs.get("defendant_id")
        organization = self.request.user.organizations.first()

        # Check if case exists and user has access
        try:
            case = Case.objects.get(id=case_id)
            if case.organization != organization:
                raise PermissionDenied("You do not have permission to access this case.")
            defendant = get_object_or_404(CaseDefendant, id=defendant_id, case=case)
        except Case.DoesNotExist:
            raise PermissionDenied("Case not found or you do not have permission to access it.")

        return DefendantInsurance.objects.filter(defendant=defendant).select_related(
            "defendant",
            "insurance_company",
            # "legal_representation",
            # "legal_representation__law_firm",
            # "legal_representation__attorney",
            # "legal_representation__co_counsel_law_firm",
            # "legal_representation__co_counsel_attorney",
            "adjusters",
        )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return DefendantInsuranceReadSerializer
        return DefendantInsuranceSerializer

    def perform_create(self, serializer):
        defendant = get_object_or_404(
            CaseDefendant, id=self.kwargs.get("defendant_id"), case__id=self.kwargs.get("case_id")
        )
        serializer.save(defendant=defendant)


class DefendantInsuranceDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
        return DefendantInsurance.objects.filter(defendant=defendant)

    def get_serializer_class(self):
        if self.request.method == "GET":
            return DefendantInsuranceReadSerializer
        return DefendantInsuranceSerializer


# First, let's create a custom pagination class
class CommonPagination(PageNumberPagination):
    """
    Common pagination class for all views that need pagination.
    Provides consistent pagination behavior across the application.
    """

    page_size = 10  # Default page size
    page_size_query_param = "page_size"  # Allow client to override page size using this query parameter
    max_page_size = 100  # Maximum limit on page size


class MedicalProviderPagination(PageNumberPagination):
    page_size = 10  # Default page size
    page_size_query_param = "page_size"  # Allow client to override page size using this query parameter
    max_page_size = 100  # Maximum limit on page size


# Then modify the MedicalProviderViewSet to use this pagination
class MedicalProviderViewSet(viewsets.ModelViewSet):
    serializer_class = MedicalProviderSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = MedicalProviderPagination  # Use our custom pagination class
    filterset_fields = {}  # Remove all filterset_fields as we'll handle them in get_queryset
    search_fields = ["company", "specialties", "note", "contacts__city"]
    ordering_fields = ["company", "created_at"]
    ordering = ["company"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return MedicalProvider.objects.none()

        queryset = MedicalProvider.objects.filter(organization=organization).prefetch_related("contacts").distinct()

        # Handle company filter
        search_query = Q()
        company = self.request.query_params.get("company__icontains")
        if company:
            company = unquote(company)  # Decode URL-encoded characters
            search_query |= Q(company__icontains=company)

        # Handle specialties filter (comma-separated)
        specialties = self.request.query_params.get("specialties", "") + self.request.query_params.get(
            "company__icontains", ""
        )
        if specialties:
            specialties = unquote(specialties)  # Decode URL-encoded characters
            specialty_list = [s.strip() for s in specialties.split(",")]
            specialty_query = Q()
            for specialty in specialty_list:
                specialty_query |= Q(specialties__icontains=specialty)
            search_query |= specialty_query  # apply or to get all result union

        # Handle city filter
        city = self.request.query_params.get("contacts__city", "") + self.request.query_params.get(
            "company__icontains", ""
        )
        if city:
            city = unquote(city)  # Decode URL-encoded characters
            search_query |= Q(contacts__city=city)

            search_query |= Q(contacts__city__icontains=city)
            search_query |= Q(contacts__street1__icontains=city)
            search_query |= Q(contacts__street2__icontains=city)
            search_query |= Q(contacts__zip_code__icontains=city)
            search_query |= Q(contacts__zip_code__icontains=city)

        # Handle state filter
        state = self.request.query_params.get("contacts__state", "") + self.request.query_params.get(
            "company__icontains", ""
        )
        if state:
            state = unquote(state)  # Decode URL-encoded characters
            search_query |= Q(contacts__state=state)

        # Handle boolean filters
        accepts_liens = self.request.query_params.get("accepts_liens")
        if accepts_liens is not None:
            accepts_liens = accepts_liens.lower() == "true"
            search_query |= Q(accepts_liens=accepts_liens)

        no_records_service = self.request.query_params.get("no_records_service")
        if no_records_service is not None:
            no_records_service = no_records_service.lower() == "true"
            search_query |= Q(no_records_service=no_records_service)

        do_not_use = self.request.query_params.get("do_not_use")
        if do_not_use is not None:
            do_not_use = do_not_use.lower() == "true"
            search_query |= Q(do_not_use=do_not_use)
            queryset = queryset.filter(search_query)

        return queryset.filter(search_query)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")
        serializer.save(organization=organization)

    @action(detail=False, methods=["post"])
    def fetch_all_cases_for_medical_provider(self, request):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response([])

        # Get medical provider ID from query parameters
        medical_provider_id = request.data.get("medical_provider_id")
        case_id = request.data.get("case_id")
        if not medical_provider_id:
            return Response({"error": "medical_provider_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Verify the medical provider belongs to the user's organization
        try:
            medical_provider = MedicalProvider.objects.get(id=medical_provider_id, organization=organization)
        except MedicalProvider.DoesNotExist:
            return Response({"error": "Medical provider not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get all treatment providers for this medical provider
        treatment_providers = TreatmentProvider.objects.filter(medical_provider=medical_provider).select_related("case")

        # Calculate summary statistics
        total_original = treatment_providers.aggregate(sum=models.Sum("original_bill"))["sum"] or 0
        total_adjusted = treatment_providers.aggregate(sum=models.Sum("adjusted_bill"))["sum"] or 0
        total_insurance_paid = treatment_providers.aggregate(sum=models.Sum("insurance_paid"))["sum"] or 0
        total_final = treatment_providers.aggregate(sum=models.Sum("still_owed"))["sum"] or 0

        # Calculate reduction percentage if original bills exist
        avg_reduction = 0
        if total_original > 0:
            avg_reduction = (total_adjusted / total_original) * 100

        # Count total cases, open cases, and dropped cases
        case_ids = treatment_providers.values_list("case_id", flat=True).distinct()
        total_cases = len(case_ids)
        open_cases = (
            Case.objects.filter(id__in=case_ids)
            .exclude(organization_status__kpi_type__in=[KPIType.case_closed, KPIType.case_dropped])
            .count()
        )
        dropped_cases = Case.objects.filter(id__in=case_ids, organization_status__kpi_type=KPIType.case_dropped).count()

        # Calculate average per case
        avg_per_case = 0
        if total_cases > 0:
            avg_per_case = total_final / total_cases

        # Prepare summary data
        summary = {
            "money_paid": total_final,
            "total_cases": total_cases,
            "avg_per_case": avg_per_case,
            "open_cases": open_cases,
            "avg_reduction": avg_reduction,
            "dropped_cases": dropped_cases,
            "total_adjusted": total_adjusted,
            "total_original": total_original,
            "total_insurance_paid": total_insurance_paid,
        }

        # Extract cases with financial details
        cases = []
        for case_id in case_ids:
            case = Case.objects.get(id=case_id)
            provider_data = treatment_providers.filter(case_id=case_id).first()

            case_data = CaseListSerializer(case).data
            case_data.update(
                {
                    "original": provider_data.original_bill,
                    "case_name": f"{case.client_basic_details.first_name} {case.client_basic_details.last_name} - {case.incident_details.incident_date.strftime('%m/%d/%Y')}",
                    "adjusted": provider_data.adjusted_bill,
                    "health_ins": provider_data.insurance_paid,
                    "final": provider_data.final_cost,
                    "still_owed": provider_data.still_owed,
                    "record_status": provider_data.records_status,
                    "billing_status": provider_data.billing_status,
                    "reduction": (
                        (provider_data.original_bill - provider_data.adjusted_bill) / provider_data.original_bill * 100
                    )
                    if provider_data.original_bill
                    and provider_data.original_bill > 0
                    and provider_data.adjusted_bill is not None
                    else 0,
                }
            )
            cases.append(case_data)

        # Apply pagination to the cases
        paginator = self.pagination_class()
        if paginator:
            page = paginator.paginate_queryset(cases, request)
            return Response(
                {
                    "summary": summary,
                    "cases": page,
                    "count": len(cases),
                    "next": paginator.get_next_link(),
                    "previous": paginator.get_previous_link(),
                }
            )

        return Response({"summary": summary, "cases": cases})


class MedicalProviderContactViewSet(viewsets.ModelViewSet):
    serializer_class = MedicalProviderContactSerializer
    permission_classes = [IsAuthenticated, NestedOrganizationPermission]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return MedicalProviderContact.objects.none()

        medical_provider_id = self.kwargs.get("medical_provider_pk")
        medical_provider = MedicalProvider.objects.filter(id=medical_provider_id, organization=organization).first()

        if not medical_provider:
            return MedicalProviderContact.objects.none()

        return MedicalProviderContact.objects.filter(medical_provider=medical_provider)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")

        medical_provider = get_object_or_404(
            MedicalProvider,
            id=self.kwargs.get("medical_provider_pk"),
            organization=organization,
        )
        serializer.save(medical_provider=medical_provider)

    def create(self, request, *args, **kwargs):
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")

        try:
            medical_provider = MedicalProvider.objects.get(
                id=self.kwargs.get("medical_provider_pk"),
                organization=organization,
            )
        except MedicalProvider.DoesNotExist:
            raise Http404("Medical provider not found")

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(medical_provider=medical_provider)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")

        try:
            medical_provider = MedicalProvider.objects.get(
                id=self.kwargs.get("medical_provider_pk"),
                organization=organization,
            )
        except MedicalProvider.DoesNotExist:
            raise Http404("Medical provider not found")

        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(medical_provider=medical_provider)
        return Response(serializer.data)


class LienHolderViewSet(viewsets.ModelViewSet):
    serializer_class = LienHolderSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None
    filterset_fields = ["company"]
    search_fields = ["company", "first_name", "last_name", "email", "phone"]
    ordering_fields = ["company", "created_at"]
    ordering = ["company"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return LienHolder.objects.none()
        return LienHolder.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class SubrogationContactViewSet(viewsets.ModelViewSet):
    serializer_class = SubrogationContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None
    filterset_fields = ["first_name", "last_name"]
    search_fields = ["first_name", "last_name", "email", "phone"]
    ordering_fields = ["first_name", "last_name", "created_at"]
    ordering = ["last_name", "first_name"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return SubrogationContact.objects.none()
        return SubrogationContact.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class OrgCostContactViewSet(viewsets.ModelViewSet):
    serializer_class = OrgCostContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    filterset_fields = ["contact_type", "company_name"]
    search_fields = ["company_name", "payee", "email", "phone"]
    ordering_fields = ["company_name", "contact_type", "created_at"]
    ordering = []  # Remove the default ordering

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return OrgCostContact.objects.none()

        queryset = OrgCostContact.objects.filter(organization=organization)
        contact_type = self.request.query_params.get("contact_type")
        if contact_type:
            queryset = queryset.filter(contact_type=contact_type)

        # Apply custom ordering with nulls last
        return queryset.order_by(models.F("company_name").asc(nulls_last=True), "created_at")

    def filter_queryset(self, queryset):
        """
        Apply custom search behavior to handle the generic relation scenario.
        """
        # Get the search term before applying other filters
        search_term = self.request.query_params.get("search", "")

        if search_term and len(search_term) >= 3:
            # Save the search parameter and remove it temporarily to prevent default search
            search_param = self.request.query_params._mutable
            self.request.query_params._mutable = True
            original_search = self.request.query_params.pop("search")
            self.request.query_params._mutable = search_param

            # Apply all other filters (except search)
            filtered_queryset = super().filter_queryset(queryset)

            # Restore the search parameter
            search_param = self.request.query_params._mutable
            self.request.query_params._mutable = True
            self.request.query_params["search"] = original_search
            self.request.query_params._mutable = search_param

            try:
                # Now perform our custom search
                from django.contrib.contenttypes.models import ContentType
                from django.db.models import Q

                # Basic search on OrgCostContact fields
                direct_matches = filtered_queryset.filter(
                    Q(company_name__icontains=search_term)
                    | Q(payee__icontains=search_term)
                    | Q(email__icontains=search_term)
                    | Q(phone__icontains=search_term)
                )

                # Search through related MedicalProviderContact records
                mp_content_type = ContentType.objects.get(
                    app_label="case_management_v2", model="medicalprovidercontact"
                )

                from case_management.v2.models import MedicalProvider, MedicalProviderContact

                # Find MedicalProvider with matching company name
                matching_providers = MedicalProvider.objects.filter(company__icontains=search_term)
                matching_contacts = MedicalProviderContact.objects.filter(medical_provider__in=matching_providers)

                # Find OrgCostContact linked to those MedicalProviderContact records
                related_matches = filtered_queryset.filter(
                    content_type=mp_content_type, object_id__in=matching_contacts.values_list("id", flat=True)
                )

                # Combine both result sets and ensure proper ordering
                return (
                    (direct_matches | related_matches)
                    .distinct()
                    .order_by(models.F("company_name").asc(nulls_last=True), "created_at")
                )

            except Exception as e:
                print(f"Error in custom search: {str(e)}")
                # If there's an error, fall back to the original filtered queryset
                return filtered_queryset.order_by(models.F("company_name").asc(nulls_last=True), "created_at")
        else:
            # No search term or too short, use regular filtering
            filtered_queryset = super().filter_queryset(queryset)
            return filtered_queryset.order_by(models.F("company_name").asc(nulls_last=True), "created_at")

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class MedicalProviderDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = MedicalProviderSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return MedicalProvider.objects.none()
        return MedicalProvider.objects.filter(organization=organization).prefetch_related("contacts")


class MedicalProviderContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = MedicalProviderContactSerializer
    permission_classes = [IsAuthenticated, NestedOrganizationPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return MedicalProviderContact.objects.none()
        return MedicalProviderContact.objects.filter(medical_provider__organization=organization)


class LienHolderDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = LienHolderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return LienHolder.objects.none()
        return LienHolder.objects.filter(organization=organization)


class SubrogationContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = SubrogationContactSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return SubrogationContact.objects.none()
        return SubrogationContact.objects.filter(organization=organization)


class OrgCostContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = OrgCostContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return OrgCostContact.objects.none()
        return OrgCostContact.objects.filter(organization=organization)


class TreatmentProviderListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["treatment_status", "records_status", "billing_status"]
    search_fields = ["treatment_description", "account_number"]
    ordering_fields = ["first_visit", "last_visit", "created_at"]
    ordering = ["-first_visit"]
    pagination_class = None  # Disable pagination to fix count issue

    def get_serializer_class(self):
        if self.request.method == "GET":
            return TreatmentProviderReadSerializer
        return TreatmentProviderSerializer

    def get_queryset(self):
        try:
            case = Case.objects.get(id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise PermissionDenied
            return TreatmentProvider.objects.filter(case=case).select_related("medical_provider", "lien_holder")
        except Case.DoesNotExist:
            raise Http404("Case not found")

    def perform_create(self, serializer):
        try:
            case = Case.objects.get(id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404("Case not found")  # Return 404 instead of PermissionDenied
            serializer.save(case=case)

            target_cases = self.request.data.get("target_cases", [])
            if len(target_cases) > 0:
                for target_case in target_cases:
                    target_case = get_object_or_404(Case, id=target_case)
                    if target_case.organization not in self.request.user.organizations.all():
                        raise PermissionDenied
                    treatment_provider = TreatmentProvider.objects.create(
                        case=target_case,
                        medical_provider=serializer.validated_data.get("medical_provider"),
                        lien_holder=serializer.validated_data.get("lien_holder"),
                    )
                    treatment_provider.save()
        except Case.DoesNotExist:
            raise Http404("Case not found")
        except Exception as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error creating treatment provider: {str(e)}", exc_info=True)
            raise


class TreatmentProviderDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    http_method_names = ["get", "patch", "delete"]  # Explicitly allow PATCH instead of PUT

    def get_serializer_class(self):
        if self.request.method == "GET":
            return TreatmentProviderReadSerializer
        return TreatmentProviderSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return TreatmentProvider.objects.filter(case=case).select_related("medical_provider", "lien_holder")

    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(TreatmentProviderReadSerializer(instance=instance).data)


class HealthInsuranceListCreateView(generics.ListCreateAPIView):
    serializer_class = HealthInsuranceSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["no_insurance", "plan_type", "erisa", "medicare"]
    search_fields = ["insured", "group_number", "member_number", "policy_number", "file_number"]
    ordering_fields = ["created_at", "total_lien", "adjusted_lien"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return HealthInsuranceReadSerializer
        return HealthInsuranceSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return HealthInsurance.objects.filter(case=case).select_related(
            "insurance_company", "subrogation_company", "representative"
        )

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        serializer.save(case=case)

        # Handle target cases if provided
        target_cases = self.request.data.get("target_cases", [])
        if len(target_cases) > 0:
            for target_case in target_cases:
                target_case = get_object_or_404(Case, id=target_case)
                if target_case.organization not in self.request.user.organizations.all():
                    raise PermissionDenied
                health_insurance = HealthInsurance.objects.create(
                    case=target_case,
                    insurance_company=serializer.validated_data.get("insurance_company"),
                )
                health_insurance.save()


class HealthInsuranceDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return HealthInsuranceReadSerializer
        return HealthInsuranceSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return HealthInsurance.objects.filter(case=case).select_related(
            "insurance_company", "subrogation_company", "representative"
        )


class EmployerListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["lost_wages_status", "income_type", "unemployed"]
    search_fields = ["company_name", "contact_first_name", "contact_last_name"]
    ordering_fields = ["company_name", "created_at"]
    ordering = ["company_name"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return EmployerReadSerializer
        return EmployerSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return (
            Employer.objects.filter(case=case)
            .select_related(
                "case",
                "workers_compensation",
                "workers_compensation__law_firm",
                "workers_compensation__attorney",
                "workers_compensation__insurance_company",
            )
            .order_by("id")
        )

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        serializer.save(case=case)


class EmployerDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return EmployerReadSerializer
        return EmployerSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        return Employer.objects.filter(case=case).select_related(
            "case",
            "workers_compensation",
            "workers_compensation__law_firm",
            "workers_compensation__attorney",
            "workers_compensation__insurance_company",
        )


class EmployerWorkersCompensationView(generics.GenericAPIView):
    serializer_class = EmployerWorkersCompensationSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_object(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        employer = get_object_or_404(Employer, id=self.kwargs["employer_id"], case=case)
        obj, created = EmployerWorkersCompensation.objects.get_or_create(employer=employer)
        return obj

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        employer = get_object_or_404(Employer, id=self.kwargs["employer_id"], case=case)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(employer=employer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CaseCostListCreateView(generics.ListCreateAPIView):
    serializer_class = CaseCostSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = CommonPagination  # Use our common pagination class
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]  # Added this line
    filterset_fields = ["payment_type", "status", "priority", "is_void", "cost_for"]
    search_fields = [
        "memo",
        "check_number",
        "credit_card_reference",
        "invoice_number",
        "contact__company_name",
        "contact__payee",
    ]
    ordering_fields = ["invoice_date", "created_at", "amount"]
    ordering = ["-created_at"]

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return CaseCost.objects.filter(case=case).select_related("contact").order_by("id")

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied

        paid_created_by = None
        if serializer.validated_data.get("paid_date"):
            paid_created_by = self.request.user
        request_created_by = None
        if serializer.validated_data.get("requested_date"):
            request_created_by = self.request.user

        serializer.save(paid_created_by=paid_created_by, request_created_by=request_created_by, case=case)


class CaseCostDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = CaseCostSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        return CaseCost.objects.filter(case=case).select_related("contact")

    def perform_update(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied

        paid_created_by = self.get_object().paid_created_by
        if (
            serializer.validated_data.get("paid_date")
            and serializer.validated_data.get("paid_date") != self.get_object().paid_date
        ):
            paid_created_by = self.request.user
        request_created_by = self.get_object().request_created_by
        if (
            serializer.validated_data.get("requested_date")
            and serializer.validated_data.get("requested_date") != self.get_object().requested_date
            and self.get_object().request_created_by is None
        ):
            request_created_by = self.request.user

        serializer.save(paid_created_by=paid_created_by, request_created_by=request_created_by, case=case)


class ClientPropertyDamageView(APIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ClientPropertyDamageReadSerializer
        return ClientPropertyDamageWriteSerializer

    def get(self, request, case_id):
        """Get client property damage for a case"""
        try:
            case = Case.objects.get(id=case_id, organization__in=request.user.organizations.all())
        except Case.DoesNotExist:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        property_damage = ClientPropertyDamage.objects.filter(case=case).first()
        if not property_damage:
            return Response(None)

        serializer = ClientPropertyDamageReadSerializer(property_damage)
        return Response(serializer.data)

    def post(self, request, case_id):
        """Create client property damage for a case"""
        try:
            case = Case.objects.get(id=case_id, organization__in=request.user.organizations.all())
        except Case.DoesNotExist:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        # Check if property damage already exists
        if ClientPropertyDamage.objects.filter(case=case).exists():
            return Response(
                {"detail": "Property damage already exists for this case"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = ClientPropertyDamageWriteSerializer(data=request.data, context={"case_id": case_id})
        serializer.is_valid(raise_exception=True)
        serializer.save(case=case)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def put(self, request, case_id):
        """Update client property damage for a case"""
        try:
            case = Case.objects.get(id=case_id, organization__in=request.user.organizations.all())
        except Case.DoesNotExist:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        property_damage = ClientPropertyDamage.objects.filter(case=case).first()
        if not property_damage:
            return Response({"detail": "Property damage not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = ClientPropertyDamageWriteSerializer(
            property_damage, data=request.data, context={"case_id": case_id}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def delete(self, request, case_id):
        """Delete client property damage for a case"""
        try:
            case = Case.objects.get(id=case_id, organization__in=request.user.organizations.all())
        except Case.DoesNotExist:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        property_damage = ClientPropertyDamage.objects.filter(case=case).first()
        if not property_damage:
            return Response({"detail": "Property damage not found"}, status=status.HTTP_404_NOT_FOUND)

        property_damage.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class DefendantInsuranceAdjusterView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return DefendantInsuranceAdjusterReadSerializer
        return DefendantInsuranceAdjusterWriteSerializer

    def get_object(self):
        try:
            # First check if the case and defendant belong to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            defendant_insurance = get_object_or_404(
                DefendantInsurance, id=self.kwargs["insurance_id"], defendant=defendant
            )
            adjuster = DefendantInsuranceAdjuster.objects.filter(defendant_insurance=defendant_insurance).first()
            return adjuster
        except Case.DoesNotExist:
            raise Http404

    def get(self, request, *args, **kwargs):
        """Get adjuster assignment for defendant insurance"""
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            defendant_insurance = get_object_or_404(
                DefendantInsurance, id=self.kwargs["insurance_id"], defendant=defendant
            )
            adjuster = DefendantInsuranceAdjuster.objects.filter(defendant_insurance=defendant_insurance).first()

            if not adjuster:
                # Create a new empty record
                adjuster = DefendantInsuranceAdjuster.objects.create(
                    defendant_insurance=defendant_insurance,
                    bodily_injury=None,
                    bi_supervisor=None,
                    medpay_pip=None,
                    medpay_pip_supervisor=None,
                    property_damage=None,
                    pd_supervisor=None,
                )

            serializer = self.get_serializer(adjuster)
            return Response(serializer.data)
        except Case.DoesNotExist:
            raise Http404

    def post(self, request, *args, **kwargs):
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            defendant_insurance = get_object_or_404(
                DefendantInsurance, id=self.kwargs["insurance_id"], defendant=defendant
            )

            # Check if adjuster already exists
            if DefendantInsuranceAdjuster.objects.filter(defendant_insurance=defendant_insurance).exists():
                return Response({"detail": "Insurance adjuster already exists"}, status=status.HTTP_400_BAD_REQUEST)

            # Add defendant_insurance to request data
            data = request.data.copy()
            data["defendant_insurance"] = defendant_insurance.id

            write_serializer = DefendantInsuranceAdjusterWriteSerializer(data=data)
            write_serializer.is_valid(raise_exception=True)
            instance = write_serializer.save()

            # Use read serializer for response
            read_serializer = DefendantInsuranceAdjusterReadSerializer(instance)
            return Response(read_serializer.data, status=status.HTTP_201_CREATED)
        except (Case.DoesNotExist, CaseDefendant.DoesNotExist, DefendantInsurance.DoesNotExist):
            raise Http404

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return Response({"detail": "Insurance adjuster not found"}, status=status.HTTP_404_NOT_FOUND)

        # Add defendant_insurance to request data
        data = request.data.copy()
        data["defendant_insurance"] = self.kwargs["insurance_id"]

        write_serializer = DefendantInsuranceAdjusterWriteSerializer(instance=instance, data=data)
        write_serializer.is_valid(raise_exception=True)
        instance = write_serializer.save()

        # Use read serializer for response
        read_serializer = DefendantInsuranceAdjusterReadSerializer(instance)
        return Response(read_serializer.data)


class CaseDefendantLegalRepresentationView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseDefendantLegalRepresentationReadSerializer
        return CaseDefendantLegalRepresentationWriteSerializer

    def get_object(self):
        try:
            # First check if the case and defendant belong to the user's organization
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            legal_rep = CaseDefendantLegalRepresentation.objects.filter(defendant=defendant).first()
            return legal_rep
        except Case.DoesNotExist:
            raise Http404

    def get(self, request, *args, **kwargs):
        """Get legal representation for defendant insurance"""
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            if case.organization not in self.request.user.organizations.all():
                raise Http404

            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
            legal_rep = CaseDefendantLegalRepresentation.objects.filter(defendant=defendant).first()

            if not legal_rep:
                # Create a new empty record
                legal_rep = CaseDefendantLegalRepresentation.objects.create(
                    defendant=defendant,
                    law_firm=None,
                    attorney=None,
                    co_counsel_law_firm=None,
                    co_counsel_attorney=None,
                    legal_note=None,
                )

            serializer = self.get_serializer(legal_rep)
            return Response(serializer.data)
        except Case.DoesNotExist:
            raise Http404

    def post(self, request, *args, **kwargs):
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)

            # Check if legal representation already exists
            if CaseDefendantLegalRepresentation.objects.filter(defendant=defendant).exists():
                return Response({"detail": "Legal representation already exists"}, status=status.HTTP_400_BAD_REQUEST)

            # Add defendant to request data
            data = request.data.copy()
            data["defendant"] = defendant.id

            write_serializer = CaseDefendantLegalRepresentationWriteSerializer(data=data)
            write_serializer.is_valid(raise_exception=True)
            instance = write_serializer.save()

            # Use read serializer for response
            read_serializer = CaseDefendantLegalRepresentationReadSerializer(instance)
            return Response(read_serializer.data, status=status.HTTP_201_CREATED)
        except (Case.DoesNotExist, CaseDefendant.DoesNotExist):
            raise Http404

    def put(self, request, *args, **kwargs):
        if getattr(self, "swagger_fake_view", False):
            return Response({})

        instance = self.get_object()
        if not instance:
            return Response({"detail": "Legal representation not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get the defendant object
        try:
            case = get_object_or_404(Case, id=self.kwargs["case_id"])
            defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
        except (Case.DoesNotExist, CaseDefendant.DoesNotExist):
            return Response({"detail": "Case or defendant not found"}, status=status.HTTP_404_NOT_FOUND)

        # Create a copy of the request data and modify it
        data = request.data.copy()

        # Remove defendant_insurance if it exists - it's not needed for this model
        if "defendant_insurance" in data:
            data.pop("defendant_insurance")

        # We don't need to add the defendant field since it's already set on the instance
        # and we're updating an existing record

        write_serializer = CaseDefendantLegalRepresentationWriteSerializer(
            instance=instance,
            data=data,
            partial=True,  # Allow partial updates
        )
        write_serializer.is_valid(raise_exception=True)
        instance = write_serializer.save()

        # Add request to serializer context to access current user
        # Use read serializer directly without additional validation
        read_serializer = CaseDefendantLegalRepresentationReadSerializer(instance, context={"request": request})
        return Response(read_serializer.data)


class CaseAttorneyLienViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing attorney liens for a case.
    """

    serializer_class = CaseAttorneyLienSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination

    def get_queryset(self):
        case_id = self.kwargs.get("case_id")
        return CaseAttorneyLien.objects.filter(
            case_id=case_id, case__organization=self.request.user.organizations.first()
        ).select_related("case", "law_firm", "attorney")

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseAttorneyLienReadSerializer
        return CaseAttorneyLienSerializer

    def perform_create(self, serializer):
        case_id = self.kwargs.get("case_id")
        case = get_object_or_404(Case, id=case_id, organization=self.request.user.organizations.first())
        serializer.save(case=case)


class CaseMiscellaneousLienViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing miscellaneous liens for a case.
    """

    serializer_class = CaseMiscellaneousLienSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination

    def get_queryset(self):
        case_id = self.kwargs.get("case_id")
        return CaseMiscellaneousLien.objects.filter(
            case_id=case_id, case__organization=self.request.user.organizations.first()
        ).select_related("case", "lien_holder")

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseMiscellaneousLienReadSerializer
        return CaseMiscellaneousLienSerializer

    def perform_create(self, serializer):
        case_id = self.kwargs.get("case_id")
        case = get_object_or_404(Case, id=case_id, organization=self.request.user.organizations.first())
        serializer.save(case=case)


class CaseDiscoveryListCreateView(generics.ListCreateAPIView):
    """
    List and create discovery requests for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["discovery_type", "propounded_by", "delivery_method", "served", "due"]
    search_fields = ["description", "status_note", "defendant__first_name", "defendant__last_name"]
    ordering_fields = ["created_at", "served", "due"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseDiscoveryReadSerializer
        return CaseDiscoveryWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise Http404
        return (
            CaseDiscovery.objects.filter(case=case)
            .select_related("case", "defendant")
            .prefetch_related("workers", "responses", "extensions")
            .order_by("-created_at")
        )

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise Http404
        serializer.save(case=case)


class CaseDiscoveryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a discovery request.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseDiscoveryReadSerializer
        return CaseDiscoveryWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return (
            CaseDiscovery.objects.filter(case=case)
            .select_related("case", "defendant")
            .prefetch_related("workers", "responses", "extensions")
        )


class DiscoveryResponseListCreateView(generics.ListCreateAPIView):
    """
    List and create discovery responses for a discovery request.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = DiscoveryResponseSerializer
    pagination_class = None

    def get_queryset(self):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise Http404
        return DiscoveryResponse.objects.filter(discovery=discovery).order_by("-created_at")

    def perform_create(self, serializer):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise Http404
        serializer.save(discovery=discovery)


class DiscoveryResponseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a discovery response.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = DiscoveryResponseSerializer

    def get_queryset(self):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return DiscoveryResponse.objects.filter(discovery=discovery)


class DiscoveryExtensionListCreateView(generics.ListCreateAPIView):
    """
    List and create discovery extensions for a discovery request.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = DiscoveryExtensionSerializer
    pagination_class = None

    def get_queryset(self):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise Http404
        return DiscoverExtension.objects.filter(discovery=discovery).order_by("-created_at")

    def perform_create(self, serializer):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise Http404
        serializer.save(discovery=discovery)


class DiscoveryExtensionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a discovery extension.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = DiscoveryExtensionSerializer

    def get_queryset(self):
        discovery = get_object_or_404(CaseDiscovery, id=self.kwargs["discovery_id"])
        if discovery.case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return DiscoverExtension.objects.filter(discovery=discovery)


class CaseEventListCreateView(generics.ListCreateAPIView):
    """
    List and create events for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["event_type", "date", "status"]
    search_fields = ["description", "address", "status"]
    ordering_fields = ["date", "start_time", "created_at"]
    ordering = ["date", "start_time"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseEventReadSerializer
        return CaseEventWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return (
            CaseEvent.objects.filter(case=case)
            .select_related("case")
            .prefetch_related("workers")
            .order_by("date", "start_time")
        )

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        serializer.save(case=case)

        try:
            integration = NylasIntegration.objects.get(user=self.request.user)

            # Get calendar_id from request, default to "primary"
            calendar_id = "primary"
            case_id = self.kwargs["case_id"]
            event_type = self.request.data.get("event_type")
            case_workers = self.request.data.get("workers", [])
            start_time = self.request.data.get("start_time")
            end_time = self.request.data.get("end_time")
            date = self.request.data.get("date")
            description = self.request.data.get("description", "")
            location = self.request.data.get("address", "")
            tz = self.request.data.get("timezone", "America/Los_Angeles")  # Default to PST if not provided

            # Convert date and times to timestamps
            start_datetime = datetime.strptime(f"{date} {start_time}", "%Y-%m-%d %H:%M")
            end_datetime = datetime.strptime(f"{date} {end_time}", "%Y-%m-%d %H:%M")

            # Make timezone aware and convert to timestamps
            start_aware = timezone.make_aware(start_datetime, timezone=ZoneInfo(tz))
            end_aware = timezone.make_aware(end_datetime, timezone=ZoneInfo(tz))

            start_time_timestamp = int(start_aware.timestamp())
            end_time_timestamp = int(end_aware.timestamp())

            participants = [
                {
                    "email": User.objects.get(id=id_).email,
                    "name": User.objects.get(id=id_).first_name + " " + User.objects.get(id=id_).last_name,
                    "status": "noreply",  # Default status for new invites
                }
                for id_ in case_workers
            ]

            # Create event using the correct method signature
            event = nylas_client.events.create(
                integration.grant_id,  # First positional argument
                query_params={"calendar_id": calendar_id},
                request_body={
                    "calendar_id": calendar_id,
                    "when": {
                        "start_time": start_time_timestamp,
                        "end_time": end_time_timestamp,
                    },
                    "title": event_type,
                    "description": description,
                    "location": location,
                    "participants": participants,
                },
            ).data

            # Update case event with calendar tracking details
            case_event = serializer.instance
            case_event.current_calendar_event_id = event.id
            case_event.calendar_id = calendar_id
            case_event.nylas_grant_id = integration.grant_id

            # Initialize or update previous_calendar_events
            if case_event.previous_calendar_events is None:
                case_event.previous_calendar_events = []
            case_event.save()

        except NylasIntegration.DoesNotExist:
            # User doesn't have Nylas integration set up, just continue without creating calendar event
            pass
        except Exception as e:
            # Log other errors but don't prevent the case event from being created
            error_logger.error(
                f"Error creating Nylas calendar event: {str(e)}",
                extra={"user_id": self.request.user.id, "case_id": self.kwargs["case_id"]},
            )


class CaseEventDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a case event.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseEventReadSerializer
        return CaseEventWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied
        return CaseEvent.objects.filter(case=case).select_related("case").prefetch_related("workers")

    def perform_update(self, serializer):
        case_event = self.get_object()
        timing_changed = False

        # Check if timing fields were updated
        serializer.save()
        case_workers = self.request.data.get("workers", [])

        participants = [
            {
                "email": User.objects.get(id=id_).email,
                "name": User.objects.get(id=id_).first_name + " " + User.objects.get(id=id_).last_name,
                "status": "noreply",  # Default status for new invites
            }
            for id_ in case_workers
        ]
        integration = NylasIntegration.objects.get(user=self.request.user)

        calendar_action = self.request.data.get("create_or_update_calendar_event", "update")

        date = self.request.data.get("date")
        start_time = self.request.data.get("start_time")
        end_time = self.request.data.get("end_time")
        tz = self.request.data.get("timezone", "America/Los_Angeles")
        description = self.request.data.get("description", "")
        location = self.request.data.get("address", "")
        event_type = self.request.data.get("event_type")

        # Convert date and times to timestamps, handling optional seconds
        try:
            start_datetime = datetime.strptime(f"{date} {start_time}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                start_datetime = datetime.strptime(f"{date} {start_time}", "%Y-%m-%d %H:%M")
            except ValueError:
                start_datetime = datetime.strptime(f"{date} {start_time}", "%Y-%m-%d %H")

        try:
            end_datetime = datetime.strptime(f"{date} {end_time}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                end_datetime = datetime.strptime(f"{date} {end_time}", "%Y-%m-%d %H:%M")
            except ValueError:
                end_datetime = datetime.strptime(f"{date} {end_time}", "%Y-%m-%d %H")

        start_aware = timezone.make_aware(start_datetime, timezone=ZoneInfo(tz))
        end_aware = timezone.make_aware(end_datetime, timezone=ZoneInfo(tz))

        if "date" in self.request.data or "start_time" in self.request.data or "end_time" in self.request.data:
            # Convert case event times to datetime objects
            try:
                event_start = datetime.strptime(f"{case_event.date} {case_event.start_time}", "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    event_start = datetime.strptime(f"{case_event.date} {case_event.start_time}", "%Y-%m-%d %H:%M")
                except ValueError:
                    event_start = datetime.strptime(f"{case_event.date} {case_event.start_time}", "%Y-%m-%d %H")

            try:
                event_end = datetime.strptime(f"{case_event.date} {case_event.end_time}", "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    event_end = datetime.strptime(f"{case_event.date} {case_event.end_time}", "%Y-%m-%d %H:%M")
                except ValueError:
                    event_end = datetime.strptime(f"{case_event.date} {case_event.end_time}", "%Y-%m-%d %H")

            event_start_aware = timezone.make_aware(event_start, timezone=ZoneInfo(tz))
            event_end_aware = timezone.make_aware(event_end, timezone=ZoneInfo(tz))

            # Check if times differ by more than 1 minute
            if (
                abs((start_aware - event_start_aware).total_seconds()) > 10
                or abs((end_aware - event_end_aware).total_seconds()) > 10
            ):
                timing_changed = True

        if timing_changed:
            try:
                if calendar_action == "create":
                    # Store current calendar event ID in history before creating new one
                    if case_event.current_calendar_event_id:
                        if case_event.previous_calendar_events is None:
                            case_event.previous_calendar_events = []
                        case_event.previous_calendar_events.append(case_event.current_calendar_event_id)

                    event = nylas_client.events.create(
                        integration.grant_id,
                        query_params={"calendar_id": case_event.calendar_id or "primary"},
                        request_body={
                            "calendar_id": case_event.calendar_id or "primary",
                            "when": {
                                "start_time": int(start_aware.timestamp()),
                                "end_time": int(end_aware.timestamp()),
                            },
                            "title": event_type,
                            "description": description,
                            "location": location,
                            "participants": participants,
                        },
                    ).data

                    # Update case event with new calendar event ID
                    case_event.current_calendar_event_id = event.id

                elif calendar_action == "update" and case_event.current_calendar_event_id:
                    nylas_client.events.update(
                        integration.grant_id,
                        case_event.current_calendar_event_id,
                        query_params={"calendar_id": case_event.calendar_id or "primary"},
                        request_body={
                            "calendar_id": case_event.calendar_id or "primary",
                            "when": {
                                "start_time": int(start_aware.timestamp()),
                                "end_time": int(end_aware.timestamp()),
                            },
                            "title": case_event.event_type,
                            "description": case_event.description,
                            "location": case_event.address,
                            "participants": participants,
                        },
                    )

            except NylasIntegration.DoesNotExist:
                pass
            except Exception as e:
                error_logger.error(
                    f"Error updating Nylas calendar event: {str(e)}",
                    extra={
                        "user_id": self.request.user.id,
                        "case_id": self.kwargs["case_id"],
                        "case_event_id": case_event.id,
                    },
                )

        else:
            nylas_client.events.update(
                integration.grant_id,
                case_event.current_calendar_event_id,
                query_params={"calendar_id": case_event.calendar_id or "primary"},
                request_body={
                    "calendar_id": case_event.calendar_id or "primary",
                    "when": {
                        "start_time": int(start_aware.timestamp()),
                        "end_time": int(end_aware.timestamp()),
                    },
                    "title": event_type,
                    "description": description,
                    "location": location,
                    "participants": participants,
                },
            ).data


class CourtContactViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing court contacts at the organization level.
    """

    serializer_class = CourtContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        return CourtContact.objects.filter(organization=self.request.user.organizations.first())


class JudgeContactViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing judge contacts at the organization level.
    """

    serializer_class = JudgeContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        return JudgeContact.objects.filter(organization=self.request.user.organizations.first())


class ClerkContactViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing clerk contacts at the organization level.
    """

    serializer_class = ClerkContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        return ClerkContact.objects.filter(organization=self.request.user.organizations.first())


class MediatorContactViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing mediator contacts at the organization level.
    """

    serializer_class = MediatorContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        return MediatorContact.objects.filter(organization=self.request.user.organizations.first())


class CaseCourtDetailsView(viewsets.ModelViewSet):
    """
    ViewSet for managing court details for a case.
    Supports multiple court details per case.

    list:
    Return a list of court details for a case.
    The response includes the court details with their contact information.

    create:
    Create a new court details entry for a case.
    Requires court ID, case number (optional), case name (optional), and trial note (optional).

    retrieve:
    Return the specified court details for a case.
    The response includes the court details with their contact information.

    update:
    Update the specified court details for a case.
    Requires court ID, case number (optional), case name (optional), and trial note (optional).

    partial_update:
    Partially update the specified court details for a case.
    Can update court ID, case number, case name, and/or trial note.

    destroy:
    Delete the specified court details for a case.
    """

    serializer_class = CaseCourtDetailsWriteSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseCourtDetailsReadSerializer
        return CaseCourtDetailsWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        return CaseCourtDetails.objects.filter(case=case).select_related("court")

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        serializer.save(case=case)


class CaseJudgeDetailsListCreateView(generics.ListCreateAPIView):
    """
    List and create judge details for a case.
    Multiple judge details can be associated with a case.

    get:
    Return a list of judge details for a case.
    The response includes the judge and clerk details with their contact information.

    post:
    Create a new judge details entry for a case.
    Requires judge ID, clerk ID (optional), and deliberation note (optional).
    """

    serializer_class = CaseJudgeDetailsWriteSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseJudgeDetailsReadSerializer
        return CaseJudgeDetailsWriteSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        context["case"] = case
        return context

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return CaseJudgeDetails.objects.none()
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        return CaseJudgeDetails.objects.filter(case=case).select_related("judge", "clerk")

    def perform_create(self, serializer):
        if getattr(self, "swagger_fake_view", False):
            return
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        serializer.save(case=case)


class CaseJudgeDetailsDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete judge details for a case.
    Multiple judge details can be associated with a case.

    get:
    Return the specified judge details for a case.
    The response includes the judge and clerk details with their contact information.

    put:
    Update the specified judge details for a case.
    Requires judge ID, clerk ID (optional), and deliberation note (optional).

    patch:
    Partially update the specified judge details for a case.
    Can update judge ID, clerk ID, and/or deliberation note.

    delete:
    Delete the specified judge details for a case.
    """

    serializer_class = CaseJudgeDetailsWriteSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseJudgeDetailsReadSerializer
        return CaseJudgeDetailsWriteSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        context["case"] = case
        return context

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return CaseJudgeDetails.objects.none()
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        return CaseJudgeDetails.objects.filter(case=case).select_related("judge", "clerk")

    def perform_update(self, serializer):
        if getattr(self, "swagger_fake_view", False):
            return
        serializer.save()

    def perform_destroy(self, instance):
        if getattr(self, "swagger_fake_view", False):
            return
        instance.delete()


class CaseMediatorDetailsView(generics.RetrieveUpdateAPIView, generics.CreateAPIView):
    serializer_class = CaseMediatorDetailsWriteSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseMediatorDetailsReadSerializer
        return CaseMediatorDetailsWriteSerializer

    def get_object(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        return get_object_or_404(CaseMediatorDetails, case=case)

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization != self.request.user.organizations.first():
            raise Http404
        serializer.save(case=case)


class BaseNegotiationListCreateView(generics.ListCreateAPIView):
    """
    Base class for negotiation list/create views.
    Provides common functionality for both regular and UIM negotiations.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["type", "status", "assigned_to"]
    search_fields = ["notes"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return self.model.objects.none()

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to view negotiations for this case")

        # Determine which model we're using and build the appropriate queryset
        if self.model.__name__ == "CaseNegotiation":
            queryset = (
                self.model.objects.filter(defendant__case=case)
                .select_related(
                    "defendant", "defendant__case", "assigned_to", "created_by", "previous_offer", "adjuster"
                )
                .order_by("-created_at")
            )
            # Handle defendant filtering for regular negotiations
            defendant_id = self.request.query_params.get("defendant")
            if defendant_id:
                queryset = queryset.filter(defendant_id=defendant_id)
        else:  # CaseNegotiationUIM
            queryset = (
                self.model.objects.filter(client_insurance__case=case)
                .select_related(
                    "client_insurance",
                    "client_insurance__case",
                    "assigned_to",
                    "created_by",
                    "previous_offer",
                    "adjuster",
                )
                .order_by("-created_at")
            )
            # Handle client_insurance filtering for UIM negotiations
            client_insurance_id = self.request.query_params.get("client_insurance_id")
            if client_insurance_id:
                queryset = queryset.filter(client_insurance_id=client_insurance_id)

        # Handle previous_offer filtering for both types
        previous_offer = self.request.query_params.get("previous_offer")
        if previous_offer == "parent":
            queryset = queryset.filter(previous_offer__isnull=True)
        elif previous_offer and previous_offer.isdigit():
            queryset = queryset.filter(previous_offer_id=previous_offer)

        return queryset

    def perform_create(self, serializer):
        if getattr(self, "swagger_fake_view", False):
            return

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to create negotiations for this case")

        # Validate case relationship based on model type
        if self.model.__name__ == "CaseNegotiation":
            defendant = serializer.validated_data.get("defendant")
            if defendant and defendant.case_id != case.id:
                raise serializers.ValidationError({"defendant": "Defendant must belong to this case"})
        else:  # CaseNegotiationUIM
            client_insurance = serializer.validated_data.get("client_insurance")
            if client_insurance and client_insurance.case_id != case.id:
                raise serializers.ValidationError({"client_insurance": "Client insurance must belong to this case"})

        serializer.save(created_by=self.request.user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        context["case"] = get_object_or_404(Case, id=self.kwargs["case_id"])
        return context


class BaseNegotiationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Base class for negotiation detail views.
    Provides common functionality for both regular and UIM negotiations.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return self.model.objects.none()

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access negotiations for this case")

        # Determine which model we're using and build the appropriate queryset
        if self.model.__name__ == "CaseNegotiation":
            return (
                self.model.objects.filter(defendant__case=case)
                .select_related(
                    "defendant", "defendant__case", "assigned_to", "created_by", "previous_offer", "adjuster"
                )
                .order_by("-created_at")
            )
        else:  # CaseNegotiationUIM
            return (
                self.model.objects.filter(client_insurance__case=case)
                .select_related(
                    "client_insurance",
                    "client_insurance__case",
                    "assigned_to",
                    "created_by",
                    "previous_offer",
                    "adjuster",
                )
                .order_by("-created_at")
            )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        context["case"] = get_object_or_404(Case, id=self.kwargs["case_id"])
        return context

    def perform_update(self, serializer):
        if getattr(self, "swagger_fake_view", False):
            return

        case = get_object_or_404(Case, id=self.kwargs["case_id"])

        if case.organization != self.request.user.organizations.first():
            raise PermissionDenied("You do not have permission to update negotiations for this case")

        if self.model.__name__ == "CaseNegotiation":
            if "defendant" in serializer.validated_data:
                defendant = serializer.validated_data["defendant"]
                if defendant.case_id != case.id:
                    raise serializers.ValidationError({"defendant": "Defendant must belong to this case"})
        else:  # CaseNegotiationUIM
            if "client_insurance" in serializer.validated_data:
                client_insurance = serializer.validated_data["client_insurance"]
                if client_insurance.case_id != case.id:
                    raise serializers.ValidationError({"client_insurance": "Client insurance must belong to this case"})

        serializer.save(
            accepted_by=self.request.user if serializer.validated_data.get("type") == "ACCEPTED_OFFER" else None,
            accepted_date=timezone.now().date() if serializer.validated_data.get("type") == "ACCEPTED_OFFER" else None,
        )

    def perform_destroy(self, instance):
        if getattr(self, "swagger_fake_view", False):
            return

        # if instance.created_by != self.request.user:
        #     raise PermissionDenied("You can only delete negotiations you created")

        # check org of created user and request user if they are same then allow operation
        if instance.created_by.organizations.first() != self.request.user.organizations.first():
            raise PermissionDenied("You can only delete negotiations you created")

        # Get all child records (counter offers) that reference this negotiation
        if self.model.__name__ == "CaseNegotiation":
            # For regular negotiations
            child_negotiations = CaseNegotiation.objects.filter(previous_offer=instance)
        else:
            # For UIM negotiations
            child_negotiations = CaseNegotiationUIM.objects.filter(previous_offer=instance)

        # Delete all child records first
        child_negotiations.delete()

        # Now delete the parent negotiation
        instance.delete()


class CaseNegotiationListCreateView(BaseNegotiationListCreateView):
    """
    List and create regular negotiations for a case.
    """

    model = CaseNegotiation

    def get_serializer_class(self):
        if self.request.method == "POST":
            return CaseNegotiationWriteSerializer
        return CaseNegotiationReadSerializer


class CaseNegotiationDetailView(BaseNegotiationDetailView):
    """
    Retrieve, update or delete a regular case negotiation.
    """

    model = CaseNegotiation

    def get_serializer_class(self):
        if self.request.method in ["PUT", "PATCH"]:
            return CaseNegotiationWriteSerializer
        return CaseNegotiationReadSerializer


class CaseNegotiationUIMListCreateView(BaseNegotiationListCreateView):
    """
    List and create UIM negotiations for a case.
    """

    model = CaseNegotiationUIM

    def get_serializer_class(self):
        if self.request.method == "POST":
            return CaseNegotiationUIMWriteSerializer
        return CaseNegotiationUIMReadSerializer


class CaseNegotiationUIMDetailView(BaseNegotiationDetailView):
    """
    Retrieve, update or delete a UIM case negotiation.
    """

    model = CaseNegotiationUIM

    def get_serializer_class(self):
        if self.request.method in ["PUT", "PATCH"]:
            return CaseNegotiationUIMWriteSerializer
        return CaseNegotiationUIMReadSerializer


class CaseIncidentDetailsView(generics.GenericAPIView):
    """
    View for managing case incident details.
    Handles:
    - GET: Retrieve incident details for a case
    - POST: Create incident details for a case
    - PUT: Update incident details for a case
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseIncidentDetailsReadSerializer
        return CaseIncidentDetailsSerializer

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return CaseIncidentDetails.objects.none()

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access incident details for this case")
        return CaseIncidentDetails.objects.filter(case=case)

    def get_object(self):
        if getattr(self, "swagger_fake_view", False):
            return None

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise Http404
        return get_object_or_404(CaseIncidentDetails, case=case)

    def get(self, request, *args, **kwargs):
        if getattr(self, "swagger_fake_view", False):
            return Response({})

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise Http404

        try:
            instance = self.get_object()
        except Http404:
            # Create a new record with current date as incident date
            instance = CaseIncidentDetails.objects.create(
                case=case,
                incident_date=timezone.now().date(),  # Set current date as incident date
                insurance_status=False,
                conflicts_checked=False,
                surgery_required=False,
                affected_areas={},
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        if getattr(self, "swagger_fake_view", False):
            return Response({})

        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to create incident details for this case")

        # Check if incident details already exist
        if CaseIncidentDetails.objects.filter(case=case).exists():
            return Response(
                {"detail": "Incident details already exist for this case"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            serializer.save(case=case)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, *args, **kwargs):
        if getattr(self, "swagger_fake_view", False):
            return Response({})

        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        context["case"] = get_object_or_404(Case, id=self.kwargs["case_id"])
        return context


class CaseStatusViewSet(viewsets.ViewSet):
    """ViewSet for managing case status and checklists"""

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_case(self, pk):
        """Helper method to get case by pk"""
        try:
            case = Case.objects.get(pk=pk, organization__in=self.request.user.organizations.all())
            return case
        except Case.DoesNotExist:
            return None

    @action(detail=True, methods=["GET"])
    def checklist(self, request, pk=None):
        """Get checklist items for current case status"""
        case = self.get_case(pk)
        if not case:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get all checklist items for the case's current status
        checklist_items = CaseChecklist.objects.filter(case=case, status=case.status)

        # Group items by required vs optional
        required_items = []
        optional_items = []

        for item in checklist_items:
            item_data = {
                "id": item.id,
                "name": item.item_name,
                "description": item.description,
                "is_completed": item.is_completed,
                "completed_at": item.completed_at,
                "completed_by": item.completed_by.email if item.completed_by else None,
            }

            if item.is_required:
                required_items.append(item_data)
            else:
                optional_items.append(item_data)

        return Response(
            {
                "case_id": case.id,
                "current_status": case.status,
                "status_display": case.get_status_display(),
                "required_items": required_items,
                "optional_items": optional_items,
            }
        )

    @action(detail=True, methods=["POST"])
    def update_status(self, request, pk=None):
        """Update case status"""
        case = self.get_case(pk)
        if not case:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        new_status = request.data.get("status")
        if not new_status:
            return Response({"detail": "New status is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate status value
        if new_status not in dict(CaseStatus.choices):
            return Response({"detail": "Invalid status value"}, status=status.HTTP_400_BAD_REQUEST)

        # Update status
        case.status = new_status
        case.save(update_fields=["status"])  # Specify update_fields to trigger signal properly

        return Response(
            {
                "detail": f"Case status updated to {case.get_status_display()}",
                "case_id": case.id,
                "status": case.status,
                "status_display": case.get_status_display(),
            }
        )

    @action(detail=True, methods=["POST"])
    def update_checklist_item(self, request, pk=None):
        """Update a checklist item's completion status"""
        case = self.get_case(pk)
        if not case:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        item_id = request.data.get("item_id")
        is_completed = request.data.get("is_completed")

        if item_id is None or is_completed is None:
            return Response(
                {"detail": "Item ID and completion status are required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            checklist_item = CaseChecklist.objects.get(id=item_id, case=case)
        except CaseChecklist.DoesNotExist:
            return Response({"detail": "Checklist item not found"}, status=status.HTTP_404_NOT_FOUND)

        # Convert is_completed to boolean if it's a string
        if isinstance(is_completed, str):
            is_completed = is_completed.lower() == "true"

        # Use mark_complete/mark_incomplete methods to ensure proper timestamp handling
        if is_completed:
            checklist_item.mark_complete(request.user)
        else:
            checklist_item.mark_incomplete()
            checklist_item.refresh_from_db()  # Refresh to ensure we have the latest state

        # Force a refresh of the case's last activity date
        case.refresh_from_db()

        return Response(
            {
                "detail": f"Checklist item {'completed' if is_completed else 'marked incomplete'}",
                "item_id": checklist_item.id,
                "name": checklist_item.item_name,
                "is_completed": checklist_item.is_completed,
                "completed_at": checklist_item.completed_at,
                "completed_by": checklist_item.completed_by.email if checklist_item.completed_by else None,
            }
        )

    @action(detail=True, methods=["POST"])
    def convert_checklist_to_task(self, request, pk=None):
        """Convert a checklist item to a high-priority task"""
        case = self.get_case(pk)
        if not case:
            return Response({"detail": "Case not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get the checklist item ID
        checklist_item_id = request.data.get("checklist_item_id")
        if not checklist_item_id:
            return Response({"detail": "checklist_item_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the checklist item and verify it belongs to this case
        try:
            checklist_item = case.checklists.get(id=checklist_item_id)
        except CaseChecklist.DoesNotExist:
            return Response({"detail": "Checklist item not found"}, status=status.HTTP_404_NOT_FOUND)

        # Create a new task
        task_description = f"Task created from checklist item: {checklist_item.item_name}\n\n"
        if checklist_item.description:
            task_description += f"Original checklist description: {checklist_item.description}"

        task = Task.objects.create(
            case=case,
            title=checklist_item.item_name,
            description=task_description,
            priority="high",  # Set as high priority
            created_by=request.user,
            assigned_to=request.user,  # Assign to the user who converted it
        )

        # Mark the checklist item as complete
        checklist_item.mark_complete(request.user)

        return Response(
            {
                "detail": "Checklist item successfully converted to task",
                "task": {"id": task.id, "title": task.title, "priority": task.priority, "status": task.status},
                "checklist_item": {
                    "id": checklist_item.id,
                    "name": checklist_item.item_name,
                    "is_completed": checklist_item.is_completed,
                    "completed_at": checklist_item.completed_at,
                },
            }
        )


class ExpertWitnessContactViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing expert witness contacts at the organization level.
    """

    serializer_class = ExpertWitnessContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["city", "state"]  # Only use these for exact matches
    search_fields = [
        "company",
        "first_name",
        "last_name",
        "specialties",
        "email",
        "city",
        "state",
    ]
    ordering_fields = ["company", "created_at", "last_name", "city"]
    ordering = ["company"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return ExpertWitnessContact.objects.none()

        queryset = ExpertWitnessContact.objects.filter(organization=organization)

        # Handle specialties search
        specialties = self.request.query_params.get("specialties")
        if specialties:
            specialty_list = [s.strip() for s in specialties.split(",")]
            specialty_query = Q()
            for specialty in specialty_list:
                specialty_query |= Q(specialties__icontains=specialty)
            queryset = queryset.filter(specialty_query)

        # Handle general search
        search = self.request.query_params.get("search")
        if search:
            search_query = (
                Q(company__icontains=search)
                | Q(first_name__icontains=search)
                | Q(last_name__icontains=search)
                | Q(specialties__icontains=search)
                | Q(email__icontains=search)
                | Q(city__icontains=search)
                | Q(state__icontains=search)
            )
            queryset = queryset.filter(search_query)

        return queryset.distinct()

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class CaseExpertWitnessListCreateView(generics.ListCreateAPIView):
    """
    List and create expert witnesses for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["type"]
    search_fields = [
        "expert_witness__company",
        "expert_witness__first_name",
        "expert_witness__last_name",
        "description",
    ]
    ordering_fields = ["created_at"]
    ordering = ["-created_at"]
    pagination_class = None
    serializer_class = CaseExpertWitnessWriteSerializer  # Add default serializer class

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseExpertWitnessReadSerializer
        return CaseExpertWitnessWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case.")
        return CaseExpertWitness.objects.filter(case=case).select_related("expert_witness")

    def perform_create(self, serializer):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to create expert witnesses for this case.")
        serializer.save(case=case)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case.")
        context["case"] = case
        return context


class CaseExpertWitnessDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a case expert witness.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = CaseExpertWitnessWriteSerializer  # Add default serializer class

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CaseExpertWitnessReadSerializer
        return CaseExpertWitnessWriteSerializer

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case.")
        return CaseExpertWitness.objects.filter(case=case).select_related("expert_witness")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if getattr(self, "swagger_fake_view", False):
            return context
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case.")
        context["case"] = case
        return context


class LoanCompanyContactListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for managing loan company contacts at the organization level.
    """

    serializer_class = LoanCompanyContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["city", "state"]
    search_fields = ["company", "payee", "phone", "email", "city"]
    ordering_fields = ["company", "created_at"]
    ordering = ["company"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        return LoanCompanyContact.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        serializer.save(organization=organization)


class LoanCompanyContactDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for managing individual loan company contacts.
    """

    serializer_class = LoanCompanyContactSerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        return LoanCompanyContact.objects.filter(organization=organization)


class SettlementAdvanceListCreateView(generics.ListCreateAPIView):
    """
    List and create settlement advances for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["company"]
    search_fields = ["company__company", "note"]
    ordering_fields = ["created_at", "amount", "total_owed"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return SettlementAdvanceReadSerializer
        return SettlementAdvanceWriteSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        context["case"] = case
        return context

    def get_queryset(self):
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        organization = self.request.user.organizations.first()
        if not case.organization == organization:
            raise PermissionDenied("You do not have permission to access this case")
        return SettlementAdvance.objects.filter(case=case)

    def perform_create(self, serializer):
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        organization = self.request.user.organizations.first()
        if not case.organization == organization:
            raise PermissionDenied("You do not have permission to access this case")
        serializer.save(case=case)


class SettlementAdvanceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a settlement advance.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return SettlementAdvanceReadSerializer
        return SettlementAdvanceWriteSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        context["case"] = case
        return context

    def get_queryset(self):
        case_id = self.kwargs["case_id"]
        case = get_object_or_404(Case, id=case_id)
        organization = self.request.user.organizations.first()
        if not case.organization == organization:
            raise PermissionDenied("You do not have permission to access this case")
        return SettlementAdvance.objects.filter(case=case)


class CaseViewSet(viewsets.ModelViewSet):
    queryset = Case.objects.all()

    def get_all_linked_cases(self, case, visited_cases=None):
        """
        Recursively get all linked cases, separating direct and indirect connections.
        Returns a tuple of (direct_cases, indirect_cases).

        Args:
            case: The base case to find relationships for
            visited_cases: Set of already visited case IDs

        Returns:
            Tuple[Set[Case], Set[Case]]: Sets of direct and indirect cases
        """
        if visited_cases is None:
            visited_cases = {case.id}

        direct_cases = set()
        indirect_cases = set()

        # Get all links for this case (both directions)
        current_links = CaseLink.objects.filter(models.Q(source_case=case) | models.Q(target_case=case)).select_related(
            "source_case", "target_case"
        )

        # First pass: collect direct cases
        for link in current_links:
            connected_case = link.target_case if link.source_case == case else link.source_case
            if connected_case.id not in visited_cases:
                direct_cases.add(connected_case)
                visited_cases.add(connected_case.id)

        # Second pass: collect indirect cases
        for direct_case in direct_cases:
            # For each direct case, get its connections
            next_links = CaseLink.objects.filter(
                models.Q(source_case=direct_case) | models.Q(target_case=direct_case)
            ).select_related("source_case", "target_case")

            for link in next_links:
                connected_case = link.target_case if link.source_case == direct_case else link.source_case
                if connected_case.id not in visited_cases:
                    indirect_cases.add(connected_case)
                    visited_cases.add(connected_case.id)

                    # Recursively get cases connected to this indirect case
                    _, more_indirect = self.get_all_linked_cases(connected_case, visited_cases)
                    indirect_cases.update(more_indirect)

        return direct_cases, indirect_cases

    @action(detail=True, methods=["get"])
    def linked_cases(self, request, pk=None):
        """
        Get all cases linked to this case, separated into direct and indirect connections.

        Returns:
            Response containing:
            - direct_cases: Cases directly linked to the current case
            - indirect_cases: Cases indirectly linked through other cases
        """
        case = self.get_object()

        # Get direct and indirect cases
        direct_cases, indirect_cases = self.get_all_linked_cases(case)

        # Combine direct and indirect cases into a single set
        all_linked_cases = direct_cases.union(indirect_cases)

        # Serialize the cases using your case serializer

        response_data = {
            # the naming is fucked for this endpoint due to backward compatibility
            "all_linked_cases": [],
            "indirect_cases": [],
            "direct_cases": CaseSerializer(all_linked_cases, many=True).data,
        }

        return Response(response_data)

    @action(detail=True, methods=["post"])
    def link_case(self, request, pk=None):
        """Create a new link between cases"""
        case = self.get_object()
        serializer = CaseLinkCreateSerializer(data=request.data, context={"request": request, "case": case})

        try:
            if serializer.is_valid():
                link = serializer.save()
                return Response(CaseLinkSerializer(link).data, status=status.HTTP_201_CREATED)
            else:
                # All validation errors should be returned with a 'detail' key
                if isinstance(serializer.errors, dict):
                    if "detail" in serializer.errors:
                        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                    elif "target_case" in serializer.errors:
                        if isinstance(serializer.errors["target_case"], dict):
                            return Response(
                                {"detail": serializer.errors["target_case"]["detail"]},
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                        return Response(
                            {"detail": serializer.errors["target_case"]}, status=status.HTTP_400_BAD_REQUEST
                        )
                    else:
                        return Response({"detail": str(serializer.errors)}, status=status.HTTP_400_BAD_REQUEST)
                return Response({"detail": str(serializer.errors)}, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError as e:
            # Handle model validation errors
            if hasattr(e, "message_dict"):
                return Response({"detail": str(e.message_dict)}, status=status.HTTP_400_BAD_REQUEST)
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["delete"])
    def unlink_case(self, request, pk=None):
        """Remove link between cases"""
        case = self.get_object()
        target_case_id = request.query_params.get("target_case_id")

        if not target_case_id:
            return Response({"detail": "target_case_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Delete both forward and reverse links
        deleted_count = CaseLink.objects.filter(
            models.Q(source_case=case, target_case=target_case_id)
            | models.Q(source_case=target_case_id, target_case=case)
        ).delete()[0]

        if deleted_count > 0:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response({"detail": "Link not found"}, status=status.HTTP_404_NOT_FOUND)


class DefendantAdjusterListView(generics.ListAPIView):
    """
    List adjusters associated with a defendant's insurances.

    This endpoint can return either:
    1. All adjusters from insurance companies linked to the defendant (default)
    2. Only adjusters currently assigned to the defendant's insurances (when assigned_only=true)

    Query Parameters:
    - assigned_only: When set to 'true', returns only adjusters currently assigned to the defendant's insurances
    """

    serializer_class = DefendantAdjusterListSerializer
    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_queryset(self):
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to view adjusters for this case")

        defendant = get_object_or_404(CaseDefendant, id=self.kwargs["defendant_id"], case=case)
        assigned_only = self.request.query_params.get("assigned_only", "").lower() == "true"

        if assigned_only:
            # Get adjusters currently assigned to defendant's insurances
            adjuster_ids = set()
            for insurance in defendant.insurances.all():
                try:
                    adjuster_assignment = insurance.adjusters
                    for field in [
                        "bodily_injury",
                        "bi_supervisor",
                        "medpay_pip",
                        "medpay_pip_supervisor",
                        "property_damage",
                        "pd_supervisor",
                    ]:
                        adjuster = getattr(adjuster_assignment, field)
                        if adjuster:
                            adjuster_ids.add(adjuster.id)
                except DefendantInsuranceAdjuster.DoesNotExist:
                    continue

            return (
                AdjusterContact.objects.filter(id__in=adjuster_ids)
                .select_related("insurance_company")
                .order_by("insurance_company__name", "last_name", "first_name")
            )
        else:
            # Get all adjusters from associated insurance companies (original behavior)
            insurance_companies = defendant.insurances.values_list("insurance_company", flat=True)
            return (
                AdjusterContact.objects.filter(insurance_company__in=insurance_companies)
                .select_related("insurance_company")
                .order_by("insurance_company__name", "last_name", "first_name")
            )


class CaseSettlementCalculationView(generics.RetrieveUpdateAPIView):
    """
    View for managing case settlement calculations.
    Supports:
    - GET: Retrieve settlement calculation for a case
    - PATCH: Update fees percentage only
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    lookup_field = "case_id"
    lookup_url_kwarg = "case_id"

    def get_serializer_class(self):
        if self.request.method in ["PATCH", "PUT"]:
            return CaseSettlementCalculationUpdateFeesSerializer
        return CaseSettlementCalculationReadSerializer

    def get_object(self):
        case_id = self.kwargs["case_id"]
        # Get the case and verify organization access
        try:
            case = Case.objects.get(id=case_id)
            if not self.request.user.organizations.filter(id=case.organization.id).exists():
                raise NotFound("Case not found")
        except Case.DoesNotExist:
            raise NotFound("Case not found")

        # Get or create settlement calculation
        obj, _ = CaseSettlementCalculation.objects.get_or_create(
            case=case,
            defaults={
                "fees_percentage": Decimal("0"),  # Start with 0% fees
                "settlement_proceed": Decimal("0"),
                "settlement_fees": Decimal("0"),
                "medpay_pip_fees": Decimal("0"),
                "advanced_costs": Decimal("0"),
                "settlement_advance_loan": Decimal("0"),
                "medical_bills": Decimal("0"),
                "health_insurance_liens": Decimal("0"),
                "attorney_liens": Decimal("0"),
                "miscellaneous_liens": Decimal("0"),
            },
        )
        return obj

    def put(self, request, *args, **kwargs):
        return Response(
            {"detail": "PUT method not allowed. Use PATCH to update fees percentage."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )


class CaseV2ViewSet(LoggerMixin, viewsets.ModelViewSet):
    logger_name = "case_management"
    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = CaseCreateV2Serializer
    queryset = Case.objects.all()

    def create(self, request, *args, **kwargs):
        """Create a new case with v2 structure"""
        self.logger.info(f"Starting case creation for user {request.user.id} ({request.user.email})")

        try:
            # Validate user organization
            organization = request.user.organizations.first()
            if not organization:
                self.logger.error(f"User {request.user.id} has no organization")
                return Response({"detail": "User must belong to an organization"}, status=status.HTTP_400_BAD_REQUEST)

            # Validate request data
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                self.logger.error(f"Validation error in case creation for user {request.user.id}: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Create case
            self.logger.info("Creating case with validated data")
            case = serializer.save()

            # Log success
            self.logger.info(
                f"Case created successfully: ID={case.id}, Name={case.case_name}, Organization={organization.id}"
            )

            response_data = {
                "id": case.id,
                "case_name": case.case_name,
                "status": case.status,
                "created_at": case.created_at,
                "message": "Case created successfully",
            }
            return Response(response_data, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            error_msg = str(e.detail) if hasattr(e, "detail") else str(e)
            self.logger.error(
                f"Validation error in case creation for user {request.user.id}: {error_msg}", exc_info=True
            )
            return Response({"detail": error_msg}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            self.logger.error(f"Unexpected error in case creation for user {request.user.id}: {str(e)}", exc_info=True)
            return Response(
                {"detail": "An unexpected error occurred while creating the case. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_queryset(self):
        """
        Filter queryset based on user's organization
        """
        user = self.request.user
        organization = user.organizations.first()

        if not organization:
            self.logger.warning(f"User {user.id} attempted to access cases without an organization")
            return Case.objects.none()

        base_queryset = Case.objects.filter(organization=organization)

        # Admin sees all cases in their organization
        if user.role == "admin":
            self.logger.debug(f"Admin user {user.id} accessing all cases in organization {organization.id}")
            return base_queryset.distinct()

        # Regular users only see their own cases and cases assigned to them
        self.logger.debug(f"Regular user {user.id} accessing their cases in organization {organization.id}")
        return base_queryset.filter(models.Q(created_by=user) | models.Q(assigned_users=user)).distinct()


class ManualSettlementEntryListCreateView(generics.ListCreateAPIView):
    """
    List and create manual settlement entries for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = ManualSettlementEntrySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["for_what"]  # Add for_what as a filter field
    search_fields = ["company", "first_name", "last_name"]
    ordering_fields = ["created_at", "expense"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_queryset(self):
        """Get manual settlement entries for a specific case"""
        case_id = self.kwargs.get("case_id")
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User has no organization")

        case = get_object_or_404(Case, id=case_id, organization=organization)
        return ManualSettlementEntry.objects.filter(case=case)

    def perform_create(self, serializer):
        """Create a new manual settlement entry"""
        case_id = self.kwargs.get("case_id")
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User has no organization")

        case = get_object_or_404(Case, id=case_id, organization=organization)
        serializer.save(case=case)


class ManualSettlementEntryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a manual settlement entry.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = ManualSettlementEntrySerializer

    def get_queryset(self):
        """Get manual settlement entries for a specific case"""
        case_id = self.kwargs.get("case_id")
        organization = self.request.user.organizations.first()
        if not organization:
            raise NotFound("User has no organization")

        case = get_object_or_404(Case, id=case_id, organization=organization)
        return ManualSettlementEntry.objects.filter(case=case)


class NewLinkedCaseCreateView(LoggerMixin, generics.CreateAPIView):
    """
    Create a new case with client information, case information, and address details.
    Optionally link it with an existing case.
    """

    logger_name = "case_management"
    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = NewLinkedCaseCreateSerializer
    http_method_names = ["post", "options"]  # Explicitly allow POST method

    def post(self, request, *args, **kwargs):
        """Handle POST request for case creation"""
        # Check organization first
        organization = request.user.organizations.first()
        if not organization:
            self.logger.error(f"User {request.user.id} has no organization")
            return Response({"detail": "User must belong to an organization"}, status=status.HTTP_400_BAD_REQUEST)
        return self.create(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Create a new case with all related information"""
        self.logger.info(f"Starting new case creation for user {request.user.id} ({request.user.email})")

        try:
            # Validate request data
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                self.logger.error(f"Validation error in case creation for user {request.user.id}: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Create case and related objects
            case = serializer.save()

            # Log success
            self.logger.info(
                f"Case created successfully: ID={case.id}, Name={case.case_name}, Organization={request.user.organizations.first().id}"
            )

            response_data = {
                "id": case.id,
                "case_name": case.case_name,
                "status": case.status,
                "created_at": case.created_at,
                "message": "Case created successfully",
            }
            return Response(response_data, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            error_msg = str(e.detail) if hasattr(e, "detail") else str(e)
            self.logger.error(
                f"Validation error in case creation for user {request.user.id}: {error_msg}", exc_info=True
            )
            return Response({"detail": error_msg}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            self.logger.error(f"Unexpected error in case creation for user {request.user.id}: {str(e)}", exc_info=True)
            return Response(
                {"detail": "An unexpected error occurred while creating the case. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MatchingSearchViewSet(viewsets.GenericViewSet):
    @action(detail=False, methods=["post"])
    def client_search(self, request):
        """
        Search for matching clients by name and date of birth.

        Request Body:
            {
                "first_name": str,
                "last_name": str,
                "date_of_birth": str (YYYY-MM-DD),
                "incident_date": str (YYYY-MM-DD) - optional,
                "case_id": str - optional, exclude this case from results
            }

        Returns:
            List of matching clients with case details
        """
        organization = request.user.organizations.first()
        first_name = request.data.get("first_name", None)
        last_name = request.data.get("last_name", None)
        date_of_birth = request.data.get("date_of_birth", None)
        incident_date = request.data.get("incident_date", None)
        case_id = request.data.get("case_id", None)

        # Start with a base query that filters by organization
        base_query = Q(case__organization=organization)

        # Add conditions only if the parameters are provided
        base_query &= Q(first_name__iexact=first_name)
        base_query &= Q(last_name__iexact=last_name)
        base_query &= Q(date_of_birth=date_of_birth)

        # Exclude the specified case if case_id is provided
        if case_id:
            base_query &= ~Q(case__id=case_id)

        # If no search parameters were provided, return an empty list
        if not any([first_name, last_name, date_of_birth, incident_date]):
            return Response([])

        if incident_date:
            clients = ClientBasicDetails.objects.filter(base_query, case__incident_details__incident_date=incident_date)
        else:
            clients = ClientBasicDetails.objects.filter(base_query)

        response = []
        for client in clients:
            response.append(
                {
                    "case_id": client.case.id,
                    "case": f"{client.first_name} {client.last_name} ({client.case.incident_details.incident_date})",
                    "client_name": f"{client.first_name} {client.last_name}",
                    "date_of_birth": client.date_of_birth,
                }
            )

        return Response(response)

    @action(detail=False, methods=["post"])
    def defendant_search(self, request):
        """
        Search for matching defendants by name and date of birth.

        Request Body:
            {
                "first_name": str,
                "last_name": str,
                "date_of_birth": str (YYYY-MM-DD),
                "case_id": str - optional, exclude this case from results
            }

        Returns:
            List of matching defendants with case details
        """
        organization = request.user.organizations.first()
        first_name = request.data.get("first_name", None)
        last_name = request.data.get("last_name", None)
        date_of_birth = request.data.get("date_of_birth", None)
        case_id = request.data.get("case_id", None)

        # Search for defendants with matching names and date of birth
        query = Q(case__organization=organization)
        query &= Q(first_name__iexact=first_name)
        query &= Q(last_name__iexact=last_name)
        query &= Q(date_of_birth=date_of_birth)

        # Exclude the specified case if case_id is provided
        if case_id:
            query &= ~Q(case__id=case_id)

        # If no search parameters were provided, return an empty list
        if not any([first_name, last_name, date_of_birth]):
            return Response([])

        defendants = CaseDefendant.objects.filter(query, case__organization=organization)
        response = []
        for defendant in defendants:
            response.append(
                {
                    "case_id": defendant.case.id,
                    "case": f"{defendant.case.client_basic_details.first_name} {defendant.case.client_basic_details.last_name} ({defendant.case.incident_details.incident_date})",
                    "defendant_name": f"{defendant.first_name} {defendant.last_name}",
                    "date_of_birth": defendant.date_of_birth,
                }
            )

        return Response(response)


class ConflictViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing conflicts.
    """

    serializer_class = ConflictSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ["get", "patch", "head", "options"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return Conflict.objects.none()

        # Filter conflicts by organization
        return Conflict.objects.filter(
            Q(source_case__organization=organization) | Q(target_case__organization=organization)
        ).select_related("source_case", "target_case", "resolved_by")

    @action(detail=True, methods=["patch"])
    def resolve(self, request, pk=None):
        """
        Resolve a conflict.
        """
        conflict = self.get_object()
        serializer = ConflictResolutionSerializer(conflict, data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)
        conflict = serializer.save()
        return Response(ConflictSerializer(conflict).data)

    @action(detail=False, methods=["get"])
    def unresolved(self, request):
        """
        Get all unresolved conflicts for the organization.
        """
        organization = request.user.organizations.first()
        if not organization:
            return Response([])

        conflicts = get_unresolved_conflicts(organization)
        page = self.paginate_queryset(conflicts)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(conflicts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def case_conflicts(self, request, case_id=None):
        """
        Get all conflicts for a specific case.
        """
        if not case_id:
            case_id = request.query_params.get("case_id")
        if not case_id:
            return Response({"error": "case_id is required"}, status=400)

        # Get the case and verify organization access
        try:
            case = Case.objects.get(id=case_id)
            if case.organization not in request.user.organizations.all():
                return Response({"error": "Case not found"}, status=404)
        except Case.DoesNotExist:
            return Response({"error": "Case not found"}, status=404)

        conflicts = get_case_conflicts(case)
        serializer = self.get_serializer(conflicts, many=True)
        return Response(serializer.data)


class CaseImpactAssessmentView(generics.GenericAPIView):
    """
    View for managing case impact assessment details.
    Handles:
    - GET: Retrieve impact assessment details for a case
    - POST: Create impact assessment details for a case
    - PUT: Update impact assessment details for a case
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    pagination_class = None

    def get_serializer_class(self):
        return CaseImpactAssessmentSerializer

    def get_queryset(self):
        """
        Get the queryset for CaseImpactAssessment objects.
        Filters by organization access.
        """
        organization = self.request.user.organizations.first()
        if not organization:
            return CaseImpactAssessment.objects.none()
        return CaseImpactAssessment.objects.filter(case__organization=organization).select_related("case")

    def get_object(self):
        """
        Get the CaseImpactAssessment object for the specified case.
        Creates a new one if it doesn't exist.
        """
        case_id = self.kwargs.get("case_id")
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")

        case = get_object_or_404(
            Case.objects.filter(organization=organization),
            id=case_id,
        )
        obj, created = CaseImpactAssessment.objects.get_or_create(case=case)
        return obj

    def get(self, request, *args, **kwargs):
        """
        Retrieve impact assessment details for a case.
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        """
        Create impact assessment details for a case.
        If an assessment already exists, returns a 400 error.
        """
        case_id = self.kwargs.get("case_id")
        organization = self.request.user.organizations.first()
        if not organization:
            raise PermissionDenied("User has no organization")

        case = get_object_or_404(
            Case.objects.filter(organization=organization),
            id=case_id,
        )

        # Check if impact assessment already exists
        if CaseImpactAssessment.objects.filter(case=case).exists():
            return Response(
                {"detail": "Impact assessment already exists for this case."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(case=case)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def put(self, request, *args, **kwargs):
        """
        Update impact assessment details for a case.
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data)

    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        context["case_id"] = self.kwargs.get("case_id")
        return context


class ClientTrustListCreateView(generics.ListCreateAPIView):
    """
    List and create client trust entries for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = ClientTrustSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["is_deposit", "deposit_type"]  # Added deposit_type to filterable fields
    search_fields = ["issuer_payee", "memo", "check_number"]
    ordering_fields = ["deposit_date", "created_at", "amount"]
    ordering = ["-deposit_date", "-created_at"]
    pagination_class = None

    def get_queryset(self):
        """Get client trust entries for a specific case"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")
        return ClientTrust.objects.filter(case=case)

    def perform_create(self, serializer):
        """Create a new client trust entry"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to create entries for this case")
        created_by = self.request.user
        serializer.save(case=case, created_by=created_by)


class ClientTrustDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a client trust entry.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    serializer_class = ClientTrustSerializer

    def get_queryset(self):
        """Get client trust entries for a specific case"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")
        return ClientTrust.objects.filter(case=case)

    def perform_update(self, serializer):
        """Update a client trust entry"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to update entries for this case")
        serializer.save(case=case)

    def perform_destroy(self, instance):
        """Delete a client trust entry"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to delete entries for this case")
        instance.delete()


class ClientTrustSummaryView(APIView):
    """
    Get summary information for client trust entries of a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get(self, request, case_id):
        case = get_object_or_404(Case, id=case_id)
        if case.organization not in request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")

        # Calculate summary information
        entries = ClientTrust.objects.filter(case=case)
        total_deposits = entries.filter(is_deposit=True).aggregate(total=models.Sum("amount"))["total"] or 0
        total_debits = abs(entries.filter(is_deposit=False).aggregate(total=models.Sum("amount"))["total"] or 0)
        current_balance = entries.order_by("-deposit_date", "-created_at").first().balance if entries.exists() else 0

        # Add deposit type summaries
        third_party_deposits = (
            entries.filter(deposit_type="THIRD_PARTY").aggregate(total=models.Sum("amount"))["total"] or 0
        )
        uim_deposits = entries.filter(deposit_type="UIM").aggregate(total=models.Sum("amount"))["total"] or 0

        return Response(
            {
                "total_deposits": total_deposits,
                "total_debits": total_debits,
                "current_balance": current_balance,
                "entry_count": entries.count(),
                "third_party_deposits": third_party_deposits,
                "uim_deposits": uim_deposits,
            }
        )


class SubrogationCompanyViewSet(viewsets.ModelViewSet):
    serializer_class = SubrogationCompanySerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None
    filterset_fields = ["company"]
    search_fields = ["company", "payee", "email", "phone", "city"]
    ordering_fields = ["company", "created_at"]
    ordering = ["company"]

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return SubrogationCompany.objects.none()
        return SubrogationCompany.objects.filter(organization=organization)

    def perform_create(self, serializer):
        organization = self.request.user.organizations.first()
        if not organization:
            return Response({"error": "User has no organization"}, status=status.HTTP_400_BAD_REQUEST)
        serializer.save(organization=organization)


class SubrogationCompanyDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = SubrogationCompanySerializer
    permission_classes = [IsAuthenticated, OrganizationLevelPermission]
    pagination_class = None

    def get_queryset(self):
        organization = self.request.user.organizations.first()
        if not organization:
            return SubrogationCompany.objects.none()
        return SubrogationCompany.objects.filter(organization=organization)


class MedPayDepositListCreateView(generics.ListCreateAPIView):
    """
    List and create MedPay deposits for a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]
    filterset_fields = ["insurance_type", "status"]
    search_fields = ["note", "check_number"]
    ordering_fields = ["medpay_request_date", "medpay_deposit_date", "created_at", "amount"]
    ordering = ["-created_at"]
    pagination_class = None

    def get_serializer_class(self):
        if self.request.method == "GET":
            return MedPayDepositReadSerializer
        return MedPayDepositSerializer

    def get_queryset(self):
        """Get MedPay deposits for a specific case"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")
        return MedPayDeposit.objects.filter(case=case)

    def perform_create(self, serializer):
        """Create a new MedPay deposit"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to create entries for this case")
        if serializer.validated_data.get("medpay_deposit_date"):
            status = MedPayDepositStatus.PAID
        else:
            status = MedPayDepositStatus.INVOICE_RAISED

        client_trust = None
        if status == MedPayDepositStatus.PAID:
            if serializer.validated_data.get("insurance_type") == "CLIENT_MEDPAY":
                client_insurance = ClientInsurance.objects.get(id=serializer.validated_data.get("client_insurance").id)
            else:
                defendant_insurance = DefendantInsurance.objects.get(
                    id=serializer.validated_data.get("defendant_insurance").id
                )
            client_trust = ClientTrust.objects.create(
                case=case,
                issuer_payee=client_insurance.insurance_company.name
                if serializer.validated_data.get("insurance_type") == "CLIENT_MEDPAY"
                else defendant_insurance.insurance_company.name,
                amount=serializer.validated_data.get("amount"),
                client_trust_entry_type=ClientTrustEntryType.MEDPAY_DEPOSIT,
                deposit_date=serializer.validated_data.get("medpay_deposit_date"),
                created_by=self.request.user,
                memo=serializer.validated_data.get("note"),
                check_number=serializer.validated_data.get("check_number"),
            )
        serializer.save(case=case, client_trust_entry=client_trust, status=status)


class MedPayDepositDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a MedPay deposit.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return MedPayDepositReadSerializer
        return MedPayDepositSerializer

    def get_queryset(self):
        """Get MedPay deposits for a specific case"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")
        return MedPayDeposit.objects.filter(case=case)

    def perform_update(self, serializer):
        """Update a MedPay deposit"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to update entries for this case")

        medpay_deposit = MedPayDeposit.objects.get(id=self.kwargs["pk"])
        if (
            serializer.validated_data.get("medpay_deposit_date")
            or medpay_deposit.medpay_deposit_date
            or medpay_deposit.status == MedPayDepositStatus.PAID
        ):
            status = MedPayDepositStatus.PAID
        else:
            status = MedPayDepositStatus.INVOICE_RAISED

        if serializer.validated_data.get("insurance_type") == "CLIENT_MEDPAY":
            client_insurance = ClientInsurance.objects.get(id=serializer.validated_data.get("client_insurance").id)
        else:
            defendant_insurance = DefendantInsurance.objects.get(
                id=serializer.validated_data.get("defendant_insurance").id
            )

        client_trust = None
        if (not medpay_deposit.client_trust_entry) and status == MedPayDepositStatus.PAID:
            client_trust = ClientTrust.objects.create(
                case=case,
                issuer_payee=client_insurance.insurance_company.name
                if serializer.validated_data.get("insurance_type") == "CLIENT_MEDPAY"
                else defendant_insurance.insurance_company.name,
                amount=serializer.validated_data.get("amount"),
                client_trust_entry_type=ClientTrustEntryType.MEDPAY_DEPOSIT,
                deposit_date=serializer.validated_data.get("medpay_deposit_date"),
                created_by=self.request.user,
                memo=serializer.validated_data.get("note"),
                check_number=serializer.validated_data.get("check_number"),
            )
        serializer.save(case=case, client_trust_entry=client_trust, status=status)

    def perform_destroy(self, instance):
        """Delete a MedPay deposit"""
        case = get_object_or_404(Case, id=self.kwargs["case_id"])
        if case.organization not in self.request.user.organizations.all():
            raise PermissionDenied("You do not have permission to delete entries for this case")
        instance.delete()


class MedPayDepositSummaryView(APIView):
    """
    Get summary information for MedPay deposits of a case.
    """

    permission_classes = [IsAuthenticated, BaseV2Permission]

    def get(self, request, case_id):
        case = get_object_or_404(Case, id=case_id)
        if case.organization not in request.user.organizations.all():
            raise PermissionDenied("You do not have permission to access this case")

        # Calculate summary information
        deposits = MedPayDeposit.objects.filter(case=case)
        total_requested = deposits.aggregate(total=models.Sum("amount"))["total"] or 0
        total_paid = (
            deposits.filter(status=MedPayDepositStatus.PAID).aggregate(total=models.Sum("amount"))["total"] or 0
        )

        # Group by insurance type
        client_insurance_deposits = (
            deposits.filter(insurance_type="CLIENT_MEDPAY").aggregate(total=models.Sum("amount"))["total"] or 0
        )
        defendant_insurance_deposits = (
            deposits.filter(insurance_type="THIRD_PARTY_MEDPAY").aggregate(total=models.Sum("amount"))["total"] or 0
        )

        return Response(
            {
                "total_requested": total_requested,
                "total_paid": total_paid,
                "pending_amount": total_requested - total_paid,
                "deposit_count": deposits.count(),
                "client_insurance_deposits": client_insurance_deposits,
                "defendant_insurance_deposits": defendant_insurance_deposits,
                "paid_count": deposits.filter(status=MedPayDepositStatus.PAID).count(),
                "invoice_raised_count": deposits.filter(status=MedPayDepositStatus.INVOICE_RAISED).count(),
            }
        )
