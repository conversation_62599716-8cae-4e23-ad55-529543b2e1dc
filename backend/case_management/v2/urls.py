from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_nested import routers

from case_management.cards import urls as card_urls
from case_management.reports import urls as report_urls

from . import views
from .other_views import (
    CaseAttorneyLienSyncViewSet,
    CaseDefendantSyncViewSet,
    CaseExpertWitnessSyncViewSet,
    CaseHealthInsuranceSyncViewSet,
    CaseIncidentDetailsSyncViewSet,
    CaseMiscellaneousLienSyncViewSet,
    CaseNoteSyncViewSet,
    CasePartySyncViewSet,
    CaseStatusSyncViewSet,
    CaseWorkersSyncViewSet,
    ClientInsuranceSyncViewSet,
    TaskSyncViewSet,
    TreatmentProviderSyncViewSet,
    UserCardSyncViewSet,
    case_negotiation_utility_view,
)
from .settlement_memo_views_new import NegotiationViewSet, SettlementMemoView

app_name = "case_management_v2"

router = DefaultRouter()

# Add the new case creation endpoint
router.register(r"cases/create", views.CaseV2ViewSet, basename="case-create-v2")

router.register(r"medical-providers", views.MedicalProviderViewSet, basename="medical-provider")

# Create a nested router for medical provider contacts
medical_provider_router = routers.NestedDefaultRouter(router, r"medical-providers", lookup="medical_provider")
medical_provider_router.register(r"contacts", views.MedicalProviderContactViewSet, basename="medical-provider-contact")

# Register other top-level routes
router.register(r"lien-holders", views.LienHolderViewSet, basename="lien-holder")
router.register(r"subrogation-contacts", views.SubrogationContactViewSet, basename="subrogation-contact")
router.register(r"cost-contacts", views.OrgCostContactViewSet, basename="cost-contact")
router.register(r"court-contacts", views.CourtContactViewSet, basename="court-contact")
router.register(r"judge-contacts", views.JudgeContactViewSet, basename="judge-contact")
router.register(r"clerk-contacts", views.ClerkContactViewSet, basename="clerk-contact")
router.register(r"mediator-contacts", views.MediatorContactViewSet, basename="mediator-contact")
router.register(r"matching-cases", views.MatchingSearchViewSet, basename="matching-case")

router.register(
    r"cases/(?P<case_id>[^/.]+)/attorney-liens",
    views.CaseAttorneyLienViewSet,
    basename="case-attorney-lien",
)
router.register(
    r"cases/(?P<case_id>[^/.]+)/miscellaneous-liens",
    views.CaseMiscellaneousLienViewSet,
    basename="case-miscellaneous-lien",
)

router.register(r"case-status", views.CaseStatusViewSet, basename="case-status")

router.register(r"expert-witnesses", views.ExpertWitnessContactViewSet, basename="expert-witness")

router.register(r"cases", views.CaseViewSet, basename="case")

# Create a router for defendant sync views
defendant_sync_router = DefaultRouter()
defendant_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/defendants/sync", CaseDefendantSyncViewSet, basename="defendant-sync"
)


# Create a router for case negotiation utility views
case_negotiation_router = DefaultRouter()
case_negotiation_router.register(
    r"case-negotiation-utility",
    case_negotiation_utility_view.CaseNegotiationUtilityViewSet,
    basename="case-negotiation-utility",
)

# Create routers for note, task, and user card sync views
note_sync_router = DefaultRouter()
note_sync_router.register(r"cases/(?P<case_id>[^/.]+)/notes/sync", CaseNoteSyncViewSet, basename="note-sync")

task_sync_router = DefaultRouter()
task_sync_router.register(r"cases/(?P<case_id>[^/.]+)/tasks/sync", TaskSyncViewSet, basename="task-sync")

user_card_sync_router = DefaultRouter()
user_card_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/user-cards/sync", UserCardSyncViewSet, basename="user-card-sync"
)

# Create a router for client sync views
client_insurance_sync_router = DefaultRouter()
client_insurance_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/client-insurance/sync", ClientInsuranceSyncViewSet, basename="client-insurance-sync"
)

# Create routers for new sync viewsets
incident_details_sync_router = DefaultRouter()
incident_details_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/incident-details/sync",
    CaseIncidentDetailsSyncViewSet,
    basename="incident-details-sync",
)

party_sync_router = DefaultRouter()
party_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/parties/sync",
    CasePartySyncViewSet,
    basename="party-sync",
)

expert_witness_sync_router = DefaultRouter()
expert_witness_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/expert-witnesses/sync",
    CaseExpertWitnessSyncViewSet,
    basename="expert-witness-sync",
)

treatment_provider_sync_router = DefaultRouter()
treatment_provider_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/treatment-providers/sync",
    TreatmentProviderSyncViewSet,
    basename="treatment-provider-sync",
)

attorney_liens_sync_router = DefaultRouter()
attorney_liens_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/attorney-liens/sync",
    CaseAttorneyLienSyncViewSet,
    basename="attorney-liens-sync",
)

miscellaneous_liens_sync_router = DefaultRouter()
miscellaneous_liens_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/miscellaneous-liens/sync",
    CaseMiscellaneousLienSyncViewSet,
    basename="miscellaneous-liens-sync",
)

workers_sync_router = DefaultRouter()
workers_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/workers/sync",
    CaseWorkersSyncViewSet,
    basename="workers-sync",
)

status_sync_router = DefaultRouter()
status_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/case-status/sync",
    CaseStatusSyncViewSet,
    basename="status-sync",
)

health_insurance_sync_router = DefaultRouter()
health_insurance_sync_router.register(
    r"cases/(?P<case_id>[^/.]+)/health-insurance/sync",
    CaseHealthInsuranceSyncViewSet,
    basename="health-insurance-sync",
)

# Register conflict viewset
router.register(r"conflicts", views.ConflictViewSet, basename="conflict")

urlpatterns = [
    # Include reports URLs
    path("reports/", include(report_urls)),
    # Case creation endpoints
    path("cases/new-linked-case/", views.NewLinkedCaseCreateView.as_view(), name="new-case-create-linked"),
    # Include routers
    path("", include(router.urls)),
    path("", include(medical_provider_router.urls)),
    path("", include(card_urls)),
    # Include sync routers
    path("", include(defendant_sync_router.urls)),
    path("", include(case_negotiation_router.urls)),
    path("", include(note_sync_router.urls)),
    path("", include(task_sync_router.urls)),
    path("", include(user_card_sync_router.urls)),
    path("", include(client_insurance_sync_router.urls)),
    # Include new sync routers
    path("", include(incident_details_sync_router.urls)),
    path("", include(party_sync_router.urls)),
    path("", include(expert_witness_sync_router.urls)),
    path("", include(treatment_provider_sync_router.urls)),
    path("", include(attorney_liens_sync_router.urls)),
    path("", include(miscellaneous_liens_sync_router.urls)),
    path("", include(workers_sync_router.urls)),
    path("", include(status_sync_router.urls)),
    path("", include(health_insurance_sync_router.urls)),
    # Organization-Level URLs
    path("organization/users/", views.OrganizationUserListView.as_view(), name="organization-users"),
    # Insurance Companies
    path("insurance-companies/", views.InsuranceCompanyListCreateView.as_view(), name="insurance-company-list"),
    path("insurance-companies/<int:pk>/", views.InsuranceCompanyDetailView.as_view(), name="insurance-company-detail"),
    # Adjusters
    path(
        "insurance-companies/<int:insurance_company_id>/adjusters/",
        views.AdjusterContactListCreateView.as_view(),
        name="adjuster-list",
    ),
    path(
        "insurance-companies/<int:insurance_company_id>/adjusters/<int:pk>/",
        views.AdjusterContactDetailView.as_view(),
        name="adjuster-detail",
    ),
    # Law Firms
    path("law-firms/", views.LawFirmContactListCreateView.as_view(), name="law-firm-list"),
    path("law-firms/<int:pk>/", views.LawFirmContactDetailView.as_view(), name="law-firm-detail"),
    # Case-Level URLs
    path(
        "cases/<str:case_id>/client-basic-details/",
        views.ClientBasicDetailsView.as_view(),
        name="client-basic-details",
    ),
    path(
        "cases/<str:case_id>/client-contact-details/",
        views.ClientContactDetailsView.as_view(),
        name="client-contact-details",
    ),
    path("cases/<str:case_id>/workers/", views.CaseWorkersView.as_view(), name="case-workers"),
    path(
        "cases/<str:case_id>/workers/available-users/",
        views.CaseWorkersAvailableUsersView.as_view(),
        name="case-workers-available-users",
    ),
    # Add incident details URL
    path(
        "cases/<str:case_id>/incident-details/",
        views.CaseIncidentDetailsView.as_view(),
        name="case-incident-details",
    ),
    # Add impact assessment URL
    path(
        "cases/<str:case_id>/impact-assessment/",
        views.CaseImpactAssessmentView.as_view(),
        name="case-impact-assessment",
    ),
    # Court, Judge, and Mediator Details URLs
    path(
        "cases/<str:case_id>/court-details/",
        views.CaseCourtDetailsView.as_view({"get": "list", "post": "create"}),
        name="case-court-details-list",
    ),
    path(
        "cases/<str:case_id>/court-details/<int:pk>/",
        views.CaseCourtDetailsView.as_view(
            {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}
        ),
        name="case-court-details-detail",
    ),
    path(
        "cases/<str:case_id>/judge-details/",
        views.CaseJudgeDetailsListCreateView.as_view(),
        name="case-judge-details-list",
    ),
    path(
        "cases/<str:case_id>/judge-details/<int:pk>/",
        views.CaseJudgeDetailsDetailView.as_view(),
        name="case-judge-details-detail",
    ),
    path(
        "cases/<str:case_id>/mediator-details/",
        views.CaseMediatorDetailsView.as_view(),
        name="case-mediator-details",
    ),
    # Insurance-Level URLs
    path(
        "cases/<str:case_id>/insurances/",
        views.ClientInsuranceListCreateView.as_view(),
        name="client-insurance-list",
    ),
    path(
        "cases/<str:case_id>/insurances/<int:pk>/",
        views.ClientInsuranceDetailView.as_view(),
        name="client-insurance-detail",
    ),
    path(
        "cases/<str:case_id>/insurances/<int:insurance_id>/adjusters/",
        views.InsuranceAdjusterView.as_view(),
        name="insurance-adjuster",
    ),
    path(
        "cases/<str:case_id>/insurances/<int:insurance_id>/legal-representation/",
        views.InsuranceLegalRepresentationView.as_view(),
        name="insurance-legal-representation",
    ),
    path(
        "cases/<str:case_id>/insurances/<int:insurance_id>/legal-representation/<int:pk>/",
        views.InsuranceLegalRepresentationDetailView.as_view(),
        name="insurance-legal-representation-detail",
    ),
    # Organization Attorney URLs
    path(
        "organization/attorneys/",
        views.OrganizationAttorneyListCreateView.as_view(),
        name="organization-attorney-list",
    ),
    path(
        "organization/attorneys/<int:pk>/",
        views.OrganizationAttorneyDetailView.as_view(),
        name="organization-attorney-detail",
    ),
    # Defendant URLs
    path(
        "cases/<str:case_id>/defendants/",
        views.CaseDefendantListCreateView.as_view(),
        name="case-defendant-list",
    ),
    path(
        "cases/<str:case_id>/defendants/<int:pk>/",
        views.CaseDefendantDetailView.as_view(),
        name="case-defendant-detail",
    ),
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/property-damage/",
        views.DefendantPropertyDamageView.as_view(),
        name="defendant-property-damage",
    ),
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/litigation-dates/",
        views.CaseDefendantLitigationServiceDatesView.as_view(),
        name="defendant-litigation-dates",
    ),
    # Defendant Insurance URLs
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/insurances/",
        views.DefendantInsuranceListCreateView.as_view(),
        name="defendant-insurance-list",
    ),
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/insurances/<int:pk>/",
        views.DefendantInsuranceDetailView.as_view(),
        name="defendant-insurance-detail",
    ),
    # Case Party URLs
    path(
        "cases/<str:case_id>/parties/",
        views.CasePartyListCreateView.as_view(),
        name="case-party-list",
    ),
    path(
        "cases/<str:case_id>/parties/<int:pk>/",
        views.CasePartyDetailView.as_view(),
        name="case-party-detail",
    ),
    path(
        "cases/<str:case_id>/party-contacts/",
        views.CasePartyContactView.as_view(),
        name="case-party-contacts",
    ),
    path(
        "cases/<str:case_id>/party-contacts/<int:contact_id>/",
        views.CasePartyContactDetailView.as_view(),
        name="case-party-contact-detail",
    ),
    # Lien Holders
    path("lien-holders/", views.LienHolderViewSet.as_view({"get": "list", "post": "create"}), name="lien-holder-list"),
    path("lien-holders/<int:pk>/", views.LienHolderDetailView.as_view(), name="lien-holder-detail"),
    # Subrogation Contacts
    path(
        "subrogation-contacts/",
        views.SubrogationContactViewSet.as_view({"get": "list", "post": "create"}),
        name="subrogation-contact-list",
    ),
    path(
        "subrogation-contacts/<int:pk>/",
        views.SubrogationContactDetailView.as_view(),
        name="subrogation-contact-detail",
    ),
    # Subrogation Companies
    path(
        "subrogation-companies/",
        views.SubrogationCompanyViewSet.as_view({"get": "list", "post": "create"}),
        name="subrogation-company-list",
    ),
    path(
        "subrogation-companies/<int:pk>/",
        views.SubrogationCompanyDetailView.as_view(),
        name="subrogation-company-detail",
    ),
    # Organization Cost Contacts
    path(
        "cost-contacts/",
        views.OrgCostContactViewSet.as_view({"get": "list", "post": "create"}),
        name="cost-contact-list",
    ),
    path("cost-contacts/<int:pk>/", views.OrgCostContactDetailView.as_view(), name="cost-contact-detail"),
    # Case-level URLs
    path(
        "cases/<str:case_id>/treatment-providers/",
        views.TreatmentProviderListCreateView.as_view(),
        name="treatment-provider-list",
    ),
    path(
        "cases/<str:case_id>/treatment-providers/<int:pk>/",
        views.TreatmentProviderDetailView.as_view(),
        name="treatment-provider-detail",
    ),
    path(
        "cases/<str:case_id>/health-insurances/",
        views.HealthInsuranceListCreateView.as_view(),
        name="health-insurance-list",
    ),
    path(
        "cases/<str:case_id>/health-insurances/<int:pk>/",
        views.HealthInsuranceDetailView.as_view(),
        name="health-insurance-detail",
    ),
    path("cases/<str:case_id>/employers/", views.EmployerListCreateView.as_view(), name="employer-list"),
    path("cases/<str:case_id>/employers/<int:pk>/", views.EmployerDetailView.as_view(), name="employer-detail"),
    path(
        "cases/<str:case_id>/employers/<int:employer_id>/workers-compensation/",
        views.EmployerWorkersCompensationView.as_view(),
        name="employer-workers-compensation",
    ),
    path("cases/<str:case_id>/costs/", views.CaseCostListCreateView.as_view(), name="case-cost-list"),
    path("cases/<str:case_id>/costs/<int:pk>/", views.CaseCostDetailView.as_view(), name="case-cost-detail"),
    # Client Property Damage
    path(
        "cases/<str:case_id>/client-property-damage/",
        views.ClientPropertyDamageView.as_view(),
        name="client-property-damage",
    ),
    # Defendant Insurance Adjusters
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/insurance/<int:insurance_id>/adjusters/",
        views.DefendantInsuranceAdjusterView.as_view(),
        name="defendant-insurance-adjuster",
    ),
    # Discovery URLs
    path("cases/<str:case_id>/discoveries/", views.CaseDiscoveryListCreateView.as_view(), name="case-discovery-list"),
    path(
        "cases/<str:case_id>/discoveries/<int:pk>/",
        views.CaseDiscoveryDetailView.as_view(),
        name="case-discovery-detail",
    ),
    path(
        "cases/<str:case_id>/discoveries/<int:discovery_id>/responses/",
        views.DiscoveryResponseListCreateView.as_view(),
        name="discovery-response-list-create",
    ),
    path(
        "cases/<str:case_id>/discoveries/<int:discovery_id>/responses/<int:pk>/",
        views.DiscoveryResponseDetailView.as_view(),
        name="discovery-response-detail",
    ),
    path(
        "cases/<str:case_id>/discoveries/<int:discovery_id>/extensions/",
        views.DiscoveryExtensionListCreateView.as_view(),
        name="discovery-extension-list-create",
    ),
    path(
        "cases/<str:case_id>/discoveries/<int:discovery_id>/extensions/<int:pk>/",
        views.DiscoveryExtensionDetailView.as_view(),
        name="discovery-extension-detail",
    ),
    # Case Event URLs
    path("cases/<str:case_id>/events/", views.CaseEventListCreateView.as_view(), name="case-event-list"),
    path("cases/<str:case_id>/events/<int:pk>/", views.CaseEventDetailView.as_view(), name="case-event-detail"),
    # Case Negotiations
    path(
        "cases/<str:case_id>/negotiations/",
        views.CaseNegotiationListCreateView.as_view(),
        name="case-negotiation-list",
    ),
    path(
        "cases/<str:case_id>/negotiations/<int:pk>/",
        views.CaseNegotiationDetailView.as_view(),
        name="case-negotiation-detail",
    ),
    # UIM Negotiations
    path(
        "cases/<str:case_id>/negotiations-uim/",
        views.CaseNegotiationUIMListCreateView.as_view(),
        name="case-negotiation-uim-list",
    ),
    path(
        "cases/<str:case_id>/negotiations-uim/<int:pk>/",
        views.CaseNegotiationUIMDetailView.as_view(),
        name="case-negotiation-uim-detail",
    ),
    path(
        "cases/<str:case_id>/expert-witnesses/",
        views.CaseExpertWitnessListCreateView.as_view(),
        name="case-expert-witness-list",
    ),
    path(
        "cases/<str:case_id>/expert-witnesses/<int:pk>/",
        views.CaseExpertWitnessDetailView.as_view(),
        name="case-expert-witness-detail",
    ),
    # Case Defendant Legal Representation
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/legal-representation/",
        views.CaseDefendantLegalRepresentationView.as_view(),
        name="case-defendant-legal-representation",
    ),
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/legal-representation/<int:pk>/",
        views.CaseDefendantLegalRepresentationView.as_view(),
        name="case-defendant-legal-representation-detail",
    ),
    # Loan Companies
    path("loan-companies/", views.LoanCompanyContactListCreateView.as_view(), name="loan-company-list"),
    path("loan-companies/<int:pk>/", views.LoanCompanyContactDetailView.as_view(), name="loan-company-detail"),
    # Settlement Advances
    path(
        "cases/<str:case_id>/settlement-advances/",
        views.SettlementAdvanceListCreateView.as_view(),
        name="settlement-advance-list",
    ),
    path(
        "cases/<str:case_id>/settlement-advances/<int:pk>/",
        views.SettlementAdvanceDetailView.as_view(),
        name="settlement-advance-detail",
    ),
    # Add the new URL pattern for listing defendant adjusters
    path(
        "cases/<str:case_id>/defendants/<int:defendant_id>/list-all-adjusters/",
        views.DefendantAdjusterListView.as_view(),
        name="defendant-adjuster-list",
    ),
    # Add the new URL pattern for settlement calculations
    path(
        "cases/<str:case_id>/settlement-calculation/",
        views.CaseSettlementCalculationView.as_view(),
        name="case-settlement-calculation",
    ),
    # Manual Settlement Entries
    path(
        "cases/<str:case_id>/manual-settlement-entries/",
        views.ManualSettlementEntryListCreateView.as_view(),
        name="manual-settlement-entry-list",
    ),
    path(
        "cases/<str:case_id>/manual-settlement-entries/<int:pk>/",
        views.ManualSettlementEntryDetailView.as_view(),
        name="manual-settlement-entry-detail",
    ),
    # Add conflict-specific URLs if needed
    path(
        "cases/<str:case_id>/conflicts/",
        views.ConflictViewSet.as_view({"get": "case_conflicts"}),
        name="case-conflicts",
    ),
    # Client Trust URLs
    path(
        "cases/<str:case_id>/client-trust/",
        views.ClientTrustListCreateView.as_view(),
        name="client-trust-list",
    ),
    path(
        "cases/<str:case_id>/client-trust/<int:pk>/",
        views.ClientTrustDetailView.as_view(),
        name="client-trust-detail",
    ),
    path(
        "cases/<str:case_id>/client-trust/summary/",
        views.ClientTrustSummaryView.as_view(),
        name="client-trust-summary",
    ),
    # MedPay Deposit URLs
    path(
        "cases/<str:case_id>/medpay-deposits/",
        views.MedPayDepositListCreateView.as_view(),
        name="medpay-deposit-list",
    ),
    path(
        "cases/<str:case_id>/medpay-deposits/<int:pk>/",
        views.MedPayDepositDetailView.as_view(),
        name="medpay-deposit-detail",
    ),
    path(
        "cases/<str:case_id>/medpay-deposits/summary/",
        views.MedPayDepositSummaryView.as_view(),
        name="medpay-deposit-summary",
    ),
    # Settlement Memo URL
    path(
        "cases/<str:case_id>/settlement-memo/",
        SettlementMemoView.as_view(),
        name="settlement-memo",
    ),
    # Negotiation API URLs
    path(
        "cases/<str:case_id>/negotiations-api/groups/",
        NegotiationViewSet.as_view({"get": "negotiation_groups"}),
        name="negotiation-groups",
    ),
    path(
        "cases/<str:case_id>/negotiations-api/latest/",
        NegotiationViewSet.as_view({"get": "latest_negotiations"}),
        name="latest-negotiations",
    ),
    path(
        "cases/<str:case_id>/negotiations-api/chain/<int:negotiation_id>/",
        NegotiationViewSet.as_view({"get": "negotiation_chain"}),
        name="negotiation-chain",
    ),
    path(
        "cases/<str:case_id>/negotiations-api/all-chains/",
        NegotiationViewSet.as_view({"get": "all_chains"}),
        name="all-negotiation-chains",
    ),
]
